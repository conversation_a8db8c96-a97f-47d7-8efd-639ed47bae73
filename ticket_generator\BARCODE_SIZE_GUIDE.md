# 條碼大小調整指南

## 📏 **條碼大小控制**

您現在可以輕鬆調整條碼的大小！條碼大小是一個**可變參數**，可以根據您的需求進行調整。

## 🎛️ **如何調整條碼大小**

### **方法 1：使用應用程序界面**
1. 啟動標籤生成器應用程序
2. 選擇一個 **Non-Ethicals** 產品
3. 在右側調整面板中找到 **"條碼寬度比例"** 控制項
4. 調整數值：
   - **50** = 很小的條碼（0.5倍）
   - **75** = 中等條碼（0.75倍）
   - **100** = 標準條碼（1.0倍）
   - **150** = 較大條碼（1.5倍）

### **方法 2：直接修改配置文件**
編輯 `config.txt` 文件中的 `[barcode_options]` 部分：
```ini
[barcode_options]
width_scale = 0.5    # 調整這個值來改變條碼寬度
```

## 📊 **推薦設置**

根據您的需求，以下是推薦的條碼寬度設置：

| 用途 | width_scale 值 | 界面設置值 | 描述 |
|------|---------------|-----------|------|
| **緊湊標籤** | 0.3 | 30 | 非常小的條碼，節省空間 |
| **標準標籤** | 0.5 | 50 | **推薦設置**，平衡大小和可讀性 |
| **清晰標籤** | 0.7 | 70 | 較大條碼，更容易掃描 |
| **大型標籤** | 1.0 | 100 | 完整大小條碼 |

## 🔧 **技術細節**

### **條碼渲染參數**
```python
barcode_options_render = {
    'module_width': 0.2,      # 條碼線條寬度
    'module_height': 20.0,    # 條碼高度
    'quiet_zone': 3.0,        # 靜默區域
    'font_size': 0,           # 隱藏數字
    'write_text': False       # 不顯示文字
}
```

### **配置文件參數**
- `width_scale`: 控制條碼的整體寬度比例
- `height_scale`: 控制條碼的高度比例
- `position_offset_x`: 條碼水平位置偏移
- `position_offset_y`: 條碼垂直位置偏移

## 🎯 **使用建議**

### **對於您的需求**
根據您提到的"條碼太大，希望減半"，建議使用：
- **width_scale = 0.5** （界面設置值：50）
- 這將使條碼寬度減少到原來的一半

### **最佳實踐**
1. **先測試**：使用不同的寬度值生成測試標籤
2. **考慮掃描器**：確保條碼仍然可以被掃描器正確讀取
3. **保持比例**：通常不需要調整高度，只調整寬度即可
4. **保存設置**：調整後的設置會自動保存，下次使用時會記住您的偏好

## 🧪 **測試功能**

我們提供了測試腳本來幫助您找到最佳的條碼大小：

```bash
python test_barcode_size.py
```

這將生成不同寬度的條碼樣本，幫助您選擇最適合的大小。

## 📋 **當前設置**

目前的默認設置已經調整為較小的條碼：
- 條碼寬度比例：**0.5** （減半大小）
- 條碼位置：居中
- 無下方數字顯示

## 🔄 **即時調整**

所有的條碼大小調整都是**即時生效**的：
1. 調整滑桿或輸入數值
2. 立即看到預覽效果
3. 設置自動保存
4. 下次啟動時保持您的設置

---

**提示**：如果條碼太小導致掃描困難，可以適當增加 `width_scale` 值。建議在 0.4-0.7 之間找到最佳平衡點。
