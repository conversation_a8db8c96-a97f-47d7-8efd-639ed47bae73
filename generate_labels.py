import pandas as pd
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.lib.units import mm
import os
import tkinter as tk
from tkinter import filedialog, messagebox
from tkcalendar import Calendar
import textwrap
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from datetime import datetime
import sys
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont, ImageTk
import re
from io import BytesIO
from pdf2image import convert_from_bytes
from tkinter import ttk

def show_error(message):
    """顯示錯誤訊息對話框"""
    root = tk.Tk()
    root.withdraw()
    messagebox.showerror("Error", message)
    root.destroy()  # 確保關閉 Tkinter 視窗

def get_app_path():
    """獲取應用程式路徑"""
    if getattr(sys, 'frozen', False):
        # 如果是打包後的執行檔
        return os.path.dirname(sys.executable)
    else:
        # 如果是從Python直接運行
        return os.path.dirname(os.path.abspath(__file__))

def get_resource_path(relative_path):
    """獲取資源文件的絕對路徑，適用於開發環境和打包後的環境"""
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = get_app_path()
    
    return os.path.join(base_path, relative_path)

# 讀取設定檔案 (Read settings file)
def load_settings():
    settings = {}
    try:
        # 獲取應用程式路徑
        app_path = get_app_path()
        settings_path = os.path.join(app_path, 'label_settings.txt')
        
        if not os.path.exists(settings_path):
            show_error(f"Settings file not found: {settings_path}\nPlease ensure label_settings.txt is in the same directory as the application.")
            sys.exit(1)
        
        with open(settings_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    key, value = line.split('=')
                    key = key.strip()
                    value = value.strip()
                    if value.startswith('"') and value.endswith('"'):
                        settings[key] = value[1:-1]
                    else:
                        try:
                            settings[key] = float(value)
                        except ValueError:
                            settings[key] = value
        
        # 檢查字體文件是否存在
        for font_key in ['FONT_PATH', 'ARIAL_FONT_PATH']:
            if font_key in settings:
                font_path = settings[font_key]
                # 嘗試在應用程式目錄下找到字體文件
                possible_paths = [
                    os.path.join(app_path, font_path),  # 相對路徑
                    os.path.join(app_path, os.path.basename(font_path)),  # 直接使用檔案名
                ]
                
                for path in possible_paths:
                    if os.path.isfile(path):
                        settings[font_key] = path
                        break
                else:
                    show_error(f"Font file not found: {font_path}\nPlease ensure the font file is in the same directory as the application.")
                    sys.exit(1)
        
        return settings
    
    except Exception as e:
        show_error(f"Error reading settings file: {str(e)}")
        sys.exit(1)

# 載入設定 (Load settings)
settings = load_settings()

# 頁面設定 (Page Settings)
PAGE_WIDTH, PAGE_HEIGHT = A4
MARGIN_X = settings['MARGIN_X'] * mm
MARGIN_Y = settings['MARGIN_Y'] * mm
COLS = int(settings['COLS'])
ROWS = int(settings['ROWS'])
LABEL_WIDTH = (PAGE_WIDTH - 2 * MARGIN_X) / COLS
LABEL_HEIGHT = (PAGE_HEIGHT - 2 * MARGIN_Y) / ROWS

# 線條設定 (Line Settings)
LINE_MARGIN = settings['LINE_MARGIN'] * mm
LINE_THICKNESS = settings['LINE_THICKNESS']
LINE_Y_POSITION = settings['LINE_Y_POSITION'] * mm

# 文字設定 (Text Settings)
TEXT_SPACING = settings['TEXT_SPACING'] * mm
SPECIAL_TEXT = settings['SPECIAL_TEXT']
SPECIAL_MARGIN = settings['SPECIAL_MARGIN'] * mm
SPECIAL_Y_POSITION = settings['SPECIAL_Y_POSITION'] * mm
SPECIAL_BACKGROUND_PADDING = settings['SPECIAL_BACKGROUND_PADDING'] * mm
SPECIAL_MAX_FONT_SIZE = int(settings['SPECIAL_MAX_FONT_SIZE'])
SPECIAL_MIN_FONT_SIZE = int(settings['SPECIAL_MIN_FONT_SIZE'])

# 商品描述設定 (Item Description Settings)
ITEM_DESC_FONT_SIZE = int(settings['ITEM_DESC_FONT_SIZE'])
ITEM_DESC_MAX_LENGTH = int(settings['ITEM_DESC_MAX_LENGTH'])
ITEM_DESC_CHARS_PER_LINE = int(settings['ITEM_DESC_CHARS_PER_LINE'])
ITEM_DESC_Y_POSITION = settings['ITEM_DESC_Y_POSITION'] * mm
ITEM_DESC_Y_POSITION_SINGLE = settings['ITEM_DESC_Y_POSITION_SINGLE'] * mm
ITEM_DESC_LINE_SPACING = settings['ITEM_DESC_LINE_SPACING'] * mm

# 價格設定 (Price Settings)
RETAIL_FONT_SIZE = int(settings.get('RRP_FONT_SIZE'))  # 使用原有的 RRP_FONT_SIZE
RETAIL_Y_POSITION = settings.get('RRP_Y_POSITION') * mm  # 使用原有的 RRP_Y_POSITION
RETAIL_LINE_SPACING = settings.get('RRP_LINE_SPACING') * mm  # 使用原有的 RRP_LINE_SPACING
SAVE_FONT_SIZE = int(settings['SAVE_FONT_SIZE'])
SAVE_Y_POSITION = settings['SAVE_Y_POSITION'] * mm
VALID_DATE_Y_POSITION = settings['VALID_DATE_Y_POSITION'] * mm
VALID_DATE_FONT_SIZE = int(settings['VALID_DATE_FONT_SIZE'])

# 文字售價設定 (Text Sale Price Settings)
TEXT_SALE_PRICE_FONT_SIZE = int(settings.get('TEXT_SALE_PRICE_FONT_SIZE', RETAIL_FONT_SIZE))  # 預設使用一般售價字體大小
TEXT_SALE_PRICE_Y_POSITION = settings.get('TEXT_SALE_PRICE_Y_POSITION', RETAIL_Y_POSITION/mm) * mm  # 預設使用一般售價位置
TEXT_SALE_PRICE_LINE_SPACING = settings.get('TEXT_SALE_PRICE_LINE_SPACING', RETAIL_LINE_SPACING/mm) * mm  # 預設使用一般售價行距

# 商品代碼設定 (Item Code Settings)
ITEM_CODE_FONT_SIZE = int(settings.get('ITEM_CODE_FONT_SIZE', 8))  # 預設值為8
ITEM_CODE_X_POSITION = settings.get('ITEM_CODE_X_POSITION', 5) * mm  # 預設值為5mm
ITEM_CODE_Y_POSITION = settings.get('ITEM_CODE_Y_POSITION', 5) * mm  # 預設值為5mm
ITEM_CODE_FONT = settings.get('ITEM_CODE_FONT', 'Arial')  # 預設使用Arial字體

# 在設定部分加載售價設定
SALE_PRICE_FONT_SIZE = int(settings['SALE_PRICE_FONT_SIZE'])
SALE_PRICE_Y_POSITION = settings['SALE_PRICE_Y_POSITION'] * mm
SALE_PRICE_LINE_SPACING = settings['SALE_PRICE_LINE_SPACING'] * mm

# 彈出選擇檔案視窗
def select_excel_file():
    root = tk.Tk()
    root.withdraw()
    file_path = filedialog.askopenfilename(filetypes=[("Excel files", "*.xlsx *.xls")])
    root.destroy()  # 確保關閉 Tkinter 視窗
    return file_path

# 計算文字寬度
def get_text_width(c, text, font_size, font_name="LuckiestGuy"):
    c.setFont(font_name, font_size)
    return c.stringWidth(text, font_name, font_size)

# 計算最大字體大小
def calculate_max_font_size(c, text, max_width, max_height):
    font_size = SPECIAL_MAX_FONT_SIZE  # 使用設定的最大字體大小 (Use set maximum font size)
    while font_size > SPECIAL_MIN_FONT_SIZE:  # 使用設定的最小字體大小 (Use set minimum font size)
        width = get_text_width(c, text, font_size)
        if width <= max_width:
            return font_size
        font_size -= 1
    return SPECIAL_MIN_FONT_SIZE

# 畫線條
def draw_line(c, x, y, width):
    # 設定線條粗細和顏色（淺灰色）
    c.setLineWidth(LINE_THICKNESS)
    c.setStrokeColorRGB(0.8, 0.8, 0.8)  # 設定為淺灰色
    
    # 計算線條位置
    line_x1 = x + LINE_MARGIN
    line_x2 = x + width - LINE_MARGIN
    line_y = y + LINE_Y_POSITION
    
    # 設定虛線樣式 - 修改點之間的間距
    c.setDash([2, 6])  # 修改為：2點線，6點空格
    
    # 畫一條水平虛線
    c.line(line_x1, line_y, line_x2, line_y)
    
    # 重置線條樣式
    c.setDash()  # 恢復實線
    c.setStrokeColorRGB(0, 0, 0)  # 恢復黑色
    
    return line_y

# 繪製多行文字 (Draw multi-line text)
def draw_wrapped_text(c, text, x, y, font_size, max_width, font_name="LuckiestGuy", max_lines=3):
    # 設定字體 (Set font)
    c.setFont(font_name, font_size)
    
    # 計算文字寬度和是否需要換行
    text_width = get_text_width(c, text, font_size, font_name)
    
    # 針對文字售價調整換行寬度
    if font_size == TEXT_SALE_PRICE_FONT_SIZE:
        # 使用更大的寬度限制，改為 0.8
        effective_width = max_width * 0.8
        needs_wrapping = True  # 強制換行
        
        # 特別處理 "ANY X FOR Y" 格式
        if "FOR" in text:
            words = text.split()
            if len(words) >= 4:  # 確保有足夠的單詞進行分行
                # 將文字分成兩行：
                # 第一行：ANY 2
                # 第二行：FOR 6
                first_line = " ".join(words[:2])
                second_line = " ".join(words[2:])
                wrapped_lines = [first_line, second_line]
                
                # 繪製每一行
                for i, line in enumerate(wrapped_lines):
                    line_width = get_text_width(c, line, font_size, font_name)
                    line_x = x + (max_width - line_width) / 2
                    line_y = y - (i * (font_size + TEXT_SALE_PRICE_LINE_SPACING))
                    c.drawString(line_x, line_y, line)
                
                return len(wrapped_lines), True
    else:
        # 其他文字保持原有的寬度限制
        needs_wrapping = text_width > (max_width - 4 * LINE_MARGIN)
    
    # 分割文字
    if needs_wrapping:
        # 計算每行可容納的字元數
        avg_char_width = get_text_width(c, "A", font_size, font_name)
        
        # 針對文字售價調整每行字元數
        if font_size == TEXT_SALE_PRICE_FONT_SIZE:
            chars_per_line = int((effective_width - 2 * LINE_MARGIN) / avg_char_width)
            # 確保每行最少有 3 個字元
            chars_per_line = max(chars_per_line, 3)
        else:
            chars_per_line = int((max_width - 2 * LINE_MARGIN) / avg_char_width)
        
        # 嘗試在空格處分行 (Try to split at spaces)
        words = text.split()
        wrapped_lines = []
        current_line = []
        current_width = 0
        
        for word in words:
            word_width = get_text_width(c, word + " ", font_size, font_name)
            if current_width + word_width <= max_width - 4 * LINE_MARGIN:
                current_line.append(word)
                current_width += word_width
            else:
                if current_line:
                    wrapped_lines.append(" ".join(current_line))
                current_line = [word]
                current_width = word_width
                
                # 如果已經達到最大行數，將剩餘文字加入最後一行
                if len(wrapped_lines) == max_lines - 1:
                    remaining_words = [word] + words[words.index(word) + 1:]
                    wrapped_lines.append(" ".join(remaining_words))
                    break
        
        if current_line and len(wrapped_lines) < max_lines:
            wrapped_lines.append(" ".join(current_line))
            
        if not wrapped_lines:  # 如果無法在空格處分行，則使用字元數分行
            wrapped_lines = textwrap.wrap(text, width=chars_per_line, max_lines=max_lines)
            if len(wrapped_lines) == max_lines and len(text) > chars_per_line * max_lines:
                # 確保最後一行顯示省略號
                wrapped_lines[-1] = wrapped_lines[-1][:-3] + "..."
    else:
        wrapped_lines = [text]
    
    # 繪製每一行 (Draw each line)
    for i, line in enumerate(wrapped_lines):
        # 重新計算每行的實際寬度 (Recalculate actual width for each line)
        line_width = get_text_width(c, line, font_size, font_name)
        # 確保完全置中 (Ensure perfect centering)
        line_x = x + (max_width - line_width) / 2
        # 使用更大的行距 (Use larger line spacing)
        line_y = y - (i * (font_size + ITEM_DESC_LINE_SPACING))
        c.drawString(line_x, line_y, line)
    
    return len(wrapped_lines), needs_wrapping

# 畫一個標籤
def draw_label(c, x, y, item_desc, sale_price, retail, valid_date=None, item_code=None, available=None):
    # 設定邊框線條粗細和顏色
    c.setLineWidth(LINE_THICKNESS)
    c.setStrokeColorRGB(0.8, 0.8, 0.8)  # 設定為淺灰色
    c.setDash([2, 2])  # 設定虛線模式
    
    # 畫標籤邊框
    c.rect(x, y, LABEL_WIDTH, LABEL_HEIGHT)
    
    # 重置線條樣式
    c.setDash()  # 恢復實線
    c.setStrokeColorRGB(0, 0, 0)  # 恢復黑色
    
    # 設定可用寬度 (Set available width)
    max_width = LABEL_WIDTH - 2 * LINE_MARGIN
    
    # 畫線條並取得線條Y座標 (Draw line and get line Y position)
    line_y = draw_line(c, x, y, LABEL_WIDTH)
    
    # 設定特別文字 (Set special text)
    special_text = SPECIAL_TEXT
    
    # 檢查 Available 欄位的內容
    # Check Available column content
    if available:
        available_str = str(available)
        if "%" in available_str:
            # 如果包含 %，計算折扣
            # If contains %, calculate discount
            try:
                sale_price_value = float(sale_price)
                retail_value = float(retail)
                if retail_value > 0:
                    discount = int(round((1 - sale_price_value / retail_value) * 100))
                    special_text = f"    {discount}% OFF   "
            except (ValueError, TypeError):
                pass
        else:
            # 檢查是否為數字
            # Check if it's a number
            try:
                float(available_str)
                # 如果是數字，保持使用 SPECIAL_TEXT
                # If it's a number, keep using SPECIAL_TEXT
                special_text = SPECIAL_TEXT
            except ValueError:
                # 如果不是數字，直接使用該文字
                # If it's not a number, use the text directly
                special_text = available_str

    # 計算並繪製 SPECIAL 文字 (Calculate and draw SPECIAL text)
    max_height = line_y - y - SPECIAL_MARGIN
    font_size = calculate_max_font_size(c, special_text, max_width, max_height)
    
    special_width = get_text_width(c, special_text, font_size)
    special_x = x + (LABEL_WIDTH - special_width) / 2  # 確保完全置中 (Ensure perfect centering)
    special_y = y + SPECIAL_Y_POSITION
    
    # 繪製黑色背景 (Draw black background)
    c.setFillColorRGB(0, 0, 0)
    background_height = font_size + 2 * SPECIAL_BACKGROUND_PADDING
    background_y = special_y - SPECIAL_BACKGROUND_PADDING
    background_width = special_width + 2 * SPECIAL_BACKGROUND_PADDING
    background_x = special_x - SPECIAL_BACKGROUND_PADDING
    
    # 確保背景也完全置中 (Ensure background is also perfectly centered)
    c.rect(background_x, background_y, background_width, background_height, fill=True)
    
    # 繪製白色文字 (Draw white text)
    c.setFillColorRGB(1, 1, 1)
    c.setFont("LuckiestGuy", font_size)
    text_y = background_y + (background_height - font_size) / 2
    c.drawString(special_x, text_y, special_text)
    
    # 重置顏色為黑色 (Reset color to black)
    c.setFillColorRGB(0, 0, 0)
    
    # 計算並繪製商品描述 (Calculate and draw item description)
    item_desc_text = str(item_desc)[:ITEM_DESC_MAX_LENGTH]
    
    # 檢查文字是否需要換行並決定位置 (Check if text needs wrapping and determine position)
    c.setFont("LuckiestGuy", ITEM_DESC_FONT_SIZE)
    text_width = get_text_width(c, item_desc_text, ITEM_DESC_FONT_SIZE)
    desc_y_position = ITEM_DESC_Y_POSITION if text_width > max_width else ITEM_DESC_Y_POSITION_SINGLE
    
    num_lines, _ = draw_wrapped_text(c, item_desc_text, x, y + desc_y_position, 
                                ITEM_DESC_FONT_SIZE, max_width, max_lines=3)
    
    # 修改售價顯示部分
    try:
        sale_price_value = float(sale_price)
        sale_price_text = f"${sale_price_value:.2f}"
        # 使用設定檔中的售價位置和字體大小
        draw_wrapped_text(c, sale_price_text, x, y + SALE_PRICE_Y_POSITION, 
                         SALE_PRICE_FONT_SIZE, max_width)
        
        # 檢查是否顯示 RRP
        show_retail = False
        retail_value = float(retail)
        if retail_value > sale_price_value:
            show_retail = True
            retail_text = f"RRP: ${retail_value:.2f}"
            draw_wrapped_text(c, retail_text, x, y + RETAIL_Y_POSITION,  
                         RETAIL_FONT_SIZE, max_width)
            # 顯示節省金額
            save_amount = retail_value - sale_price_value
            save_text = f"SAVE ${save_amount:.2f}"
            draw_wrapped_text(c, save_text, x, y + SAVE_Y_POSITION, 
                            SAVE_FONT_SIZE, max_width)
    
    except (ValueError, TypeError):
        # 如果售價不是數字，直接顯示文字
        sale_price_text = str(sale_price)
        draw_wrapped_text(c, sale_price_text, x, y + TEXT_SALE_PRICE_Y_POSITION, 
                         TEXT_SALE_PRICE_FONT_SIZE, max_width)
    
    # 修改有效期限顯示部分 - 獨立於售價類型
    if valid_date and pd.notna(valid_date):
        try:
            if isinstance(valid_date, str):
                date_obj = pd.to_datetime(valid_date)
            else:
                date_obj = pd.Timestamp(valid_date)
            
            # 檢查日期是否有效且不是今天
            today = pd.Timestamp.now().date()
            if pd.notna(date_obj) and date_obj.date() != today:
                formatted_date = date_obj.strftime('%d %b %Y').upper()
                valid_date_text = f"Valid until: {formatted_date}"
                draw_wrapped_text(c, valid_date_text, x, y + VALID_DATE_Y_POSITION,
                                VALID_DATE_FONT_SIZE, max_width, font_name="Arial")
        except Exception as e:
            print(f"Warning: Unable to format date {valid_date}: {str(e)}")
    
    # 繪製商品代碼 (Draw item code)
    if item_code:
        c.setFont(ITEM_CODE_FONT, ITEM_CODE_FONT_SIZE)
        # 將商品代碼轉換為整數 (Convert item code to integer)
        try:
            item_code_value = int(float(item_code))
            item_code_text = str(item_code_value)
        except (ValueError, TypeError):
            item_code_text = str(item_code)
        c.drawString(x + ITEM_CODE_X_POSITION, y + ITEM_CODE_Y_POSITION, item_code_text)

def sanitize_filename(filename):
    # 移除或替換不合法的文件名字符
    filename = re.sub(r'[<>:"/\\|?*]', '', filename)
    # 將空格替換為下劃線
    filename = filename.replace(' ', '_')
    # 確保文件名不超過255字符
    if len(filename) > 255:
        filename = filename[:255]
    return filename

def draw_label_png(item_desc, sale_price, retail, valid_date=None, item_code=None, available=None, output_dir=None):
    # 調整解析度倍數（增加圖片清晰度）
    scale_factor = 8  # 您可以調整這個值來改變圖片大小，例如：2, 4, 6, 8
    
    # 創建新的 PNG 圖像（使用更高的解析度）
    width = int(LABEL_WIDTH * scale_factor)
    height = int(LABEL_HEIGHT * scale_factor)
    image = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(image)
    
    # 根據縮放因子調整所有尺寸
    scaled_line_thickness = int(LINE_THICKNESS * scale_factor)
    scaled_line_margin = int(LINE_MARGIN * scale_factor)
    scaled_special_background_padding = int(SPECIAL_BACKGROUND_PADDING * scale_factor)
    
    # 載入放大後的字體
    lucky_guy_font = ImageFont.truetype(settings['FONT_PATH'], int(ITEM_DESC_FONT_SIZE * scale_factor))
    arial_font = ImageFont.truetype(settings['ARIAL_FONT_PATH'], int(ITEM_CODE_FONT_SIZE * scale_factor))
    
    # 繪製邊框（虛線，淺灰色）
    light_gray = (204, 204, 204)  # RGB for light gray
    draw.rectangle([(0, 0), (width-1, height-1)], 
                  outline=light_gray, 
                  width=scaled_line_thickness)
    
    # 繪製水平虛線
    line_y = int(height - LINE_Y_POSITION * scale_factor)
    for x in range(scaled_line_margin, width-scaled_line_margin, 4):
        if x + 2 <= width-scaled_line_margin:
            draw.line(
                [(x, line_y), (x + 2, line_y)],
                fill=light_gray,
                width=scaled_line_thickness
            )
    
    # 設定特別文字
    special_text = SPECIAL_TEXT
    if available:
        available_str = str(available)
        if "%" in available_str:
            try:
                sale_price_value = float(sale_price)
                retail_value = float(retail)
                if retail_value > 0:
                    discount = int(round((1 - sale_price_value / retail_value) * 100))
                    special_text = f"    {discount}% OFF   "
            except (ValueError, TypeError):
                pass
        else:
            try:
                float(available_str)
                special_text = SPECIAL_TEXT
            except ValueError:
                special_text = available_str
    
    # 繪製特別文字（黑底白字）- 使用更大的字體
    special_font = ImageFont.truetype(settings['FONT_PATH'], int(SPECIAL_MAX_FONT_SIZE * scale_factor))
    special_bbox = draw.textbbox((0, 0), special_text, font=special_font)
    special_width = special_bbox[2] - special_bbox[0]
    special_height = special_bbox[3] - special_bbox[1]
    
    # 計算位置（根據縮放調整）
    special_x = (width - special_width) // 2
    special_y = height - (SPECIAL_Y_POSITION * scale_factor)
    
    # 繪製黑色背景（加大內邊距）
    padding = scaled_special_background_padding
    draw.rectangle([
        (special_x - padding, special_y - padding),
        (special_x + special_width + padding, special_y + special_height + padding)
    ], fill='black')
    
    # 繪製白色文字
    draw.text((special_x, special_y), special_text, fill='white', font=special_font)
    
    # 繪製商品描述
    item_desc_text = str(item_desc)[:ITEM_DESC_MAX_LENGTH]
    desc_font = ImageFont.truetype(settings['FONT_PATH'], int(ITEM_DESC_FONT_SIZE * scale_factor))
    desc_bbox = draw.textbbox((0, 0), item_desc_text, font=desc_font)
    desc_width = desc_bbox[2] - desc_bbox[0]
    desc_x = (width - desc_width) // 2
    desc_y = height - (ITEM_DESC_Y_POSITION * scale_factor)
    draw.text((desc_x, desc_y), item_desc_text, fill='black', font=desc_font)
    
    # 繪製價格
    try:
        sale_price_value = float(sale_price)
        sale_price_text = f"${sale_price_value:.2f}"
        price_font = ImageFont.truetype(settings['FONT_PATH'], int(RETAIL_FONT_SIZE * scale_factor))
    except (ValueError, TypeError):
        sale_price_text = str(sale_price)
        price_font = ImageFont.truetype(settings['FONT_PATH'], int(TEXT_SALE_PRICE_FONT_SIZE * scale_factor))
    
    price_bbox = draw.textbbox((0, 0), sale_price_text, font=price_font)
    price_width = price_bbox[2] - price_bbox[0]
    price_x = (width - price_width) // 2
    price_y = height - (RETAIL_Y_POSITION * scale_factor)
    draw.text((price_x, price_y), sale_price_text, fill='black', font=price_font)
    
    # 繪製 RRP
    try:
        retail_value = float(retail)
        sale_price_value = float(sale_price)
        if retail_value > sale_price_value:
            retail_text = f"RRP: ${retail_value:.2f}"
            retail_font = ImageFont.truetype(settings['FONT_PATH'], int(RETAIL_FONT_SIZE * scale_factor))
            retail_bbox = draw.textbbox((0, 0), retail_text, font=retail_font)
            retail_width = retail_bbox[2] - retail_bbox[0]
            retail_x = (width - retail_width) // 2
            retail_y = height - (RETAIL_Y_POSITION * scale_factor)
            draw.text((retail_x, retail_y), retail_text, fill='black', font=retail_font)
    except (ValueError, TypeError):
        pass  # 如果無法轉換為浮點數，則跳過 RRP 顯示
    
    # 繪製有效期限
    if valid_date and pd.notna(valid_date):  # 添加 pd.notna 檢查
        try:
            if isinstance(valid_date, str):
                date_obj = pd.to_datetime(valid_date)
            else:
                date_obj = pd.Timestamp(valid_date)
            
            # 檢查日期是否有效且不是今天
            today = pd.Timestamp.now().date()
            if pd.notna(date_obj) and date_obj.date() != today:
                formatted_date = date_obj.strftime('%d %b %Y').upper()
                valid_date_text = f"Valid until: {formatted_date}"
                
                date_font = ImageFont.truetype(settings['ARIAL_FONT_PATH'], 
                                             int(VALID_DATE_FONT_SIZE * scale_factor))
                date_bbox = draw.textbbox((0, 0), valid_date_text, font=date_font)
                date_width = date_bbox[2] - date_bbox[0]
                date_x = (width - date_width) // 2
                date_y = height - (VALID_DATE_Y_POSITION * scale_factor)
                draw.text((date_x, date_y), valid_date_text, fill='black', font=date_font)
        except Exception as e:
            print(f"Warning: Unable to format date {valid_date}: {str(e)}")
            # 不再顯示無效的日期
    
    # 繪製商品代碼
    if item_code:
        try:
            item_code_value = int(float(item_code))
            item_code_text = str(item_code_value)
        except (ValueError, TypeError):
            item_code_text = str(item_code)
        
        code_font = ImageFont.truetype(settings['ARIAL_FONT_PATH'], int(ITEM_CODE_FONT_SIZE * scale_factor))
        code_x = ITEM_CODE_X_POSITION * scale_factor
        code_y = height - (ITEM_CODE_Y_POSITION * scale_factor)
        draw.text((code_x, code_y), item_code_text, fill='black', font=code_font)
    
    # 生成文件名並保存
    filename = sanitize_filename(str(item_desc)) + '.png'
    if output_dir:
        filename = os.path.join(output_dir, filename)
    
    # 使用高質量保存設置
    image.save(filename, 'PNG', 
              dpi=(300, 300),     # 調整 DPI 值，例如：(150, 150), (300, 300), (600, 600)
              quality=95)         # 調整品質，範圍 1-95
    return filename

def get_valid_date():
    """顯示日期選擇器對話框"""
    try:
        import babel.numbers
    except ImportError:
        # 如果無法導入 babel.numbers，使用替代方案
        def format_date(date):
            return date.strftime('%d %b %Y').upper()
    else:
        def format_date(date):
            return date.strftime('%d %b %Y').upper()

    root = tk.Tk()
    root.title("Valid Date Selection")
    
    # 設置視窗背景色
    root.configure(bg='#f0f0f0')  # 使用淺灰色背景
    
    # 設置視窗置頂
    root.attributes('-topmost', True)
    
    # 創建主框架，添加陰影效果
    main_frame = tk.Frame(root, bg='white', relief=tk.SOLID, borderwidth=1)
    main_frame.pack(padx=25, pady=25, fill=tk.BOTH, expand=True)
    
    # 創建標題
    title_frame = tk.Frame(main_frame, bg='white')
    title_frame.pack(fill=tk.X, padx=20, pady=(20,0))
    
    title_label = tk.Label(title_frame, 
                          text="Valid Date Selection",
                          font=("Arial", 16, "bold"),
                          fg='#2c3e50',  # 深藍色
                          bg='white')
    title_label.pack(anchor='w')
    
    # 添加分隔線
    separator = tk.Frame(main_frame, height=2, bg='#e0e0e0')
    separator.pack(fill=tk.X, padx=20, pady=(10,20))
    
    # 創建內容框架
    content_frame = tk.Frame(main_frame, bg='white')
    content_frame.pack(fill=tk.BOTH, expand=True, padx=20)
    
    # 左側說明區域
    left_frame = tk.Frame(content_frame, bg='white')
    left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0,10))
    
    # 使用更現代的說明文字格式
    instruction_text = """How to use:
• Select a date and click 'OK' to include valid date
• Click 'Cancel' to skip valid date

Excel Integration:
You can add a "Valid Date" column in your Excel files
to set different valid dates for each item.

Format: DD/MM/YYYY
Example: 31/12/2024"""
    
    instruction_label = tk.Label(left_frame, 
                               text=instruction_text,
                               font=("Arial", 10),
                               justify=tk.LEFT,
                               bg='white',
                               fg='#2c3e50')  # 深藍色
    instruction_label.pack(anchor='w', pady=(0,15))
    
    # 載入並顯示 Valid.png 圖片
    try:
        image_path = get_resource_path('Valid.png')
        if os.path.exists(image_path):
            image = Image.open(image_path)
            max_size = (200, 200)
            image.thumbnail(max_size, Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(image)
            
            # 創建圖片框架，添加邊框效果
            image_frame = tk.Frame(left_frame, relief=tk.SOLID, borderwidth=1, bg='white')
            image_frame.pack(pady=10)
            
            image_label = tk.Label(image_frame, image=photo, bg='white')
            image_label.image = photo
            image_label.pack(padx=2, pady=2)
    except Exception as e:
        print(f"Warning: Could not load Valid.png: {str(e)}")
    
    # 右側日期選擇區域
    right_frame = tk.Frame(content_frame, bg='white')
    right_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10,0))
    
    # 自定義日曆樣式
    cal_frame = tk.Frame(right_frame, relief=tk.SOLID, borderwidth=1, bg='white')
    cal_frame.pack(pady=(0,15))
    
    current_date = datetime.now()
    cal = Calendar(cal_frame, 
                  selectmode='day',
                  year=current_date.year,
                  month=current_date.month,
                  day=current_date.day,
                  font=("Arial", 10),
                  selectbackground='#3498db',  # 藍色選擇背景
                  background='white',
                  foreground='#2c3e50',
                  borderwidth=0)
    cal.pack(padx=2, pady=2)
    
    # 創建預覽框架
    preview_frame = tk.LabelFrame(right_frame, 
                                text=" Preview ",
                                font=("Arial", 10, "bold"),
                                bg='white',
                                fg='#2c3e50',
                                relief=tk.SOLID)
    preview_frame.pack(fill=tk.X, pady=(0,15))
    
    preview_label = tk.Label(preview_frame,
                           text="Sample Item\n$9.99\nRRP: $19.99\nSAVE $10.00",
                           font=("Arial", 10),
                           bg='white',
                           fg='#2c3e50')
    preview_label.pack(pady=10)
    
    valid_date_preview = tk.Label(preview_frame,
                                text="",
                                font=("Arial", 10),
                                bg='white',
                                fg='#2c3e50')
    valid_date_preview.pack(pady=(0,10))
    
    def update_preview(*args):
        try:
            selected_date = cal.selection_get()
            formatted_date = format_date(selected_date)
            valid_date_preview.config(text=f"Valid until: {formatted_date}")
        except:
            valid_date_preview.config(text="")
    
    cal.bind('<<CalendarSelected>>', update_preview)
    
    # 添加分隔線
    separator2 = tk.Frame(main_frame, height=2, bg='#e0e0e0')
    separator2.pack(fill=tk.X, padx=20, pady=20)
    
    # 按鈕框架
    button_frame = tk.Frame(main_frame, bg='white')
    button_frame.pack(pady=(0,20), side=tk.BOTTOM)  # 添加 side=tk.BOTTOM 確保按鈕在底部
    
    result = [None]
    
    def on_ok():
        selected_date = cal.selection_get()
        today = datetime.now().date()
        if selected_date == today:
            result[0] = None
        else:
            result[0] = format_date(selected_date)
        root.destroy()
        root.quit()
    
    def on_cancel():
        root.destroy()
        root.quit()
    
    # 現代風格按鈕
    ok_button = tk.Button(button_frame, 
                         text="OK",
                         command=on_ok,
                         font=("Arial", 10),
                         width=10,
                         bg='#3498db',  # 藍色
                         fg='white',
                         relief=tk.FLAT,
                         cursor='hand2')
    ok_button.pack(side=tk.LEFT, padx=10)
    
    cancel_button = tk.Button(button_frame,
                            text="Cancel",
                            command=on_cancel,
                            font=("Arial", 10),
                            width=10,
                            bg='#e0e0e0',  # 灰色
                            fg='#2c3e50',
                            relief=tk.FLAT,
                            cursor='hand2')
    cancel_button.pack(side=tk.LEFT)

    # 調整視窗大小
    width = 800
    height = 650  # 增加視窗高度以確保有足夠空間顯示按鈕
    x = (root.winfo_screenwidth() - width) // 2
    y = (root.winfo_screenheight() - height) // 2
    root.geometry(f"{width}x{height}+{x}+{y}")
    
    # 防止視窗調整大小
    root.resizable(False, False)
    
    # 將視窗置中
    root.update_idletasks()
    root.lift()
    
    root.mainloop()
    return result[0]

def open_fred_guide():
    guide_window = tk.Tk()
    guide_window.withdraw()  # 先隱藏視窗
    guide_window.title("How to Export Data from Fred")
    
    # 設置視窗大小和位置
    width = 1600
    height = 800
    x = (guide_window.winfo_screenwidth() - width) // 2
    y = (guide_window.winfo_screenheight() - height) // 2
    guide_window.geometry(f"{width}x{height}+{x}+{y}")
    
    guide_window.configure(bg='white')
    guide_window.deiconify()  # 顯示視窗
    
    main_frame = tk.Frame(guide_window, bg='white', relief=tk.SOLID, borderwidth=1)
    main_frame.pack(padx=30, pady=30, fill=tk.BOTH, expand=True)
    
    # 標題區域
    title_frame = tk.Frame(main_frame, bg='white')
    title_frame.pack(fill=tk.X, padx=25, pady=(25,0))
    
    title_label = tk.Label(title_frame,
                          text="How to export specific product list from Inventory (Fred)",
                          font=("Arial", 16, "bold"),
                          fg='#2c3e50',
                          bg='white')
    title_label.pack(anchor='w')
    
    separator = tk.Frame(main_frame, height=2, bg='#e0e0e0')
    separator.pack(fill=tk.X, padx=25, pady=(15,25))
    
    # 內容區域分為兩列（不是三列）
    content_frame = tk.Frame(main_frame, bg='white')
    content_frame.pack(fill=tk.BOTH, expand=True, padx=25)
    
    # 左側：PNG 和注意事項（60%）
    left_frame = tk.Frame(content_frame, bg='white')
    left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0,20))
    
    # 右側：步驟說明（40%）
    right_frame = tk.Frame(content_frame, bg='white')
    right_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(20,0))
    
    # 移除右側的 Label Display Rules 部分
    # 移除 rules_title 和 rules_label
    
    # 中間步驟標題
    steps_title = tk.Label(right_frame,
                          text="Steps",
                          font=("Arial", 14, "bold"),
                          fg='#2c3e50',
                          bg='white')
    steps_title.pack(anchor='w', pady=(0,15))
    
    # 添加規則文字
    rules_text = ""

    rules_label = tk.Label(right_frame,
                          text=rules_text,
                          font=("Arial", 11),
                          fg='#34495e',
                          bg='white',
                          justify=tk.LEFT,
                          wraplength=int(width * 0.25))
    rules_label.pack(anchor='w')
    
    # 載入並顯示 interface.png 圖片
    try:
        image_path = get_resource_path('interface.png')
        if os.path.exists(image_path):
            image = Image.open(image_path)
            # 將圖片縮小到原來的 50%
            max_width = int(width * 0.5)  # 修改這裡，從 0.6 改為 0.5
            ratio = max_width / image.size[0]
            max_height = int(image.size[1] * ratio)
            
            image = image.resize((max_width, max_height), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(image)
            
            image_frame = tk.Frame(left_frame, relief=tk.SOLID, borderwidth=1, bg='white')
            image_frame.pack(pady=10)
            
            image_label = tk.Label(image_frame, image=photo, bg='white')
            image_label.image = photo
            image_label.pack(padx=3, pady=3)
            
            # 在圖片下方添加注意事項
            note_text = """Option: Add Valid Date
• Open the exported Excel and add a column called Valid Date (case sensitive)
• If the system finds Valid Date column with valid dates, it will auto-generate labels
• If not found, it will ask you to enter Valid Date manually
• If you click cancel, the label PDF will be created but without Valid Date"""
    
            note_label = tk.Label(left_frame,
                                text=note_text,
                                font=("Arial", 11),
                                fg='#34495e',
                                bg='white',
                                justify=tk.LEFT,
                                wraplength=max_width)
            note_label.pack(anchor='w', pady=(10,0))
            
    except Exception as e:
        print(f"Warning: Could not load interface.png: {str(e)}")
    
    # 步驟說明
    steps = [
        ("1. Go to Inventory", ""),
        ("2. Use Filter", "on Department, Category or Item Description to find your product list"),
        ("3. Required Columns", "Make sure these columns are showing:\n• Item Code\n• Item Description\n• Sales Price\n• Retail Price\n• Available"),
        ("4. Missing Columns?", "Right-click any column > Column Chooser > drag the missing ones in"),
        ("5. Export", "Click Export > choose Excel (.xls)"),
        ("6. Save", "Name the file and save — ready for upload ✅")
    ]
    
    # 添加每個步驟
    for step_title, step_desc in steps:
        step_frame = tk.Frame(right_frame, bg='white')
        step_frame.pack(fill=tk.X, pady=(0,15))
        
        title = tk.Label(step_frame,
                        text=step_title,
                        font=("Arial", 12, "bold"),
                        fg='#2c3e50',
                        bg='white')
        title.pack(anchor='w')
        
        if step_desc:
            desc = tk.Label(step_frame,
                          text=step_desc,
                          font=("Arial", 11),
                          fg='#34495e',
                          bg='white',
                          justify=tk.LEFT,
                          wraplength=int(width * 0.35))
            desc.pack(anchor='w', padx=(20,0))
    
    # 防止視窗調整大小
    guide_window.resizable(False, False)

    # 在最後添加返回按鈕框架
    button_frame = tk.Frame(main_frame, bg='white')
    button_frame.pack(side=tk.BOTTOM, pady=20)
    
    def return_to_main():
        guide_window.destroy()
        create_labels()
    
    return_button = tk.Button(button_frame,
                            text="Return",
                            command=return_to_main,
                            font=("Arial", 10),
                            bg='#3498db',
                            fg='white',
                            relief=tk.FLAT,
                            cursor='hand2',
                            padx=30,
                            pady=5)
    return_button.pack()
    
    def on_enter(e):
        return_button['bg'] = '#2980b9'
    
    def on_leave(e):
        return_button['bg'] = '#3498db'
    
    return_button.bind("<Enter>", on_enter)
    return_button.bind("<Leave>", on_leave)
    
    guide_window.resizable(False, False)
    guide_window.mainloop()

def open_label_rules():
    rules_window = tk.Tk()
    rules_window.withdraw()  # 先隱藏視窗
    rules_window.title("Label Display Rules")
    
    # 設置視窗大小和位置
    width = 800
    height = 600
    x = (rules_window.winfo_screenwidth() - width) // 2
    y = (rules_window.winfo_screenheight() - height) // 2
    rules_window.geometry(f"{width}x{height}+{x}+{y}")
    
    rules_window.configure(bg='white')
    rules_window.deiconify()  # 顯示視窗
    
    # 創建主框架
    main_frame = tk.Frame(rules_window, bg='white')
    main_frame.pack(padx=25, pady=25, fill=tk.BOTH, expand=True)
    
    # 標題
    title_label = tk.Label(main_frame,
                          text="Label Display Rules",
                          font=("Arial", 16, "bold"),
                          fg='#2c3e50',
                          bg='white')
    title_label.pack(pady=(0,20))
    
    # 規則文字 - 移除換行符，讓文字自然展開
    rules_text = ("RRP (Recommended Retail Price) & Save Amount:\n"
                  "• Example: If RRP $19.99 > Sale Price $9.99  →  Shows \"RRP: $19.99\" and \"SAVE $10.00\"\n"
                  "• If RRP ≤ Sale Price: No RRP or Save Amount shown\n"
                  "• If Sale Price is text (e.g., \"ANY 2 FOR $6\"): No RRP or Save Amount shown\n\n"
                  "Valid Date:\n"
                  "• Displays only when ALL conditions are met:\n"
                  "  1. Item has a discount (RRP > Sale Price)\n"
                  "  2. Valid Date is provided\n"
                  "  3. Selected date is not today\n"
                  "  4. Sale Price must be a number (not text)\n"
                  "• If today's date is selected: No Valid Date shown\n"
                  "• If no discount: No Valid Date shown\n"
                  "• If Sale Price is text: Valid Date still shown\n\n"
                  "Examples:\n"
                  "✓ RRP $19.99, Sale $9.99, Valid Date 01 APR 2024  →  Shows all: RRP, Save Amount, Valid Date\n\n"
                  "✗ RRP $9.99, Sale $9.99  →  Shows only Sale Price\n\n"
                  "✗ RRP $19.99, Sale $9.99, Valid Date: Today  →  Shows RRP and Save Amount, but no Valid Date\n\n"
                  "✗ RRP $19.99, Sale \"ANY 2 FOR $6\"  →  Shows Sale Price text, Valid Date")
    
    
    rules_label = tk.Label(main_frame,
                          text=rules_text,
                          font=("Arial", 11),
                          fg='#34495e',
                          bg='white',
                          justify=tk.LEFT,
                          wraplength=750)  # 增加 wraplength 讓文字更寬鬆
    rules_label.pack(padx=30)
    
    rules_window.resizable(False, False)
    
    # 在最後添加返回按鈕框架
    button_frame = tk.Frame(main_frame, bg='white')
    button_frame.pack(side=tk.BOTTOM, pady=20)
    
    def return_to_main():
        rules_window.destroy()
        create_labels()
    
    return_button = tk.Button(button_frame,
                            text="Return",
                            command=return_to_main,
                            font=("Arial", 10),
                            bg='#3498db',
                            fg='white',
                            relief=tk.FLAT,
                            cursor='hand2',
                            padx=30,
                            pady=5)
    return_button.pack()
    
    def on_enter(e):
        return_button['bg'] = '#2980b9'
    
    def on_leave(e):
        return_button['bg'] = '#3498db'
    
    return_button.bind("<Enter>", on_enter)
    return_button.bind("<Leave>", on_leave)
    
    rules_window.resizable(False, False)
    rules_window.mainloop()

def create_labels():
    try:
        root = tk.Tk()
        root.title("Label Generator")
        root.configure(bg='#f0f0f0')
        
        main_frame = tk.Frame(root, bg='white', relief=tk.SOLID, borderwidth=1)
        main_frame.pack(padx=25, pady=25, fill=tk.BOTH, expand=True)
        
        # 標題部分
        title_frame = tk.Frame(main_frame, bg='white')
        title_frame.pack(fill=tk.X, padx=20, pady=(20,0))
        
        title_label = tk.Label(title_frame,
                             text="Lee Pharmacy & Better Value Pharmacy",
                             font=("Arial", 16, "bold"),
                             fg='#2c3e50',
                             bg='white')
        title_label.pack()
        
        subtitle_label = tk.Label(title_frame,
                                text="Promotion Label Generator",
                                font=("Arial", 14),
                                fg='#34495e',
                                bg='white')
        subtitle_label.pack(pady=(5,0))
        
        # 分隔線
        separator = tk.Frame(main_frame, height=2, bg='#e0e0e0')
        separator.pack(fill=tk.X, padx=20, pady=20)
        
        # 內容區域（包含開發者資訊和規則）
        content_frame = tk.Frame(main_frame, bg='white')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20)
        
        # 左側說明區域
        left_frame = tk.Frame(content_frame, bg='white')
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0,10))
        
        # 作者資訊，使用現代卡片設計
        author_frame = tk.Frame(left_frame, bg='white', relief=tk.SOLID, borderwidth=1)
        author_frame.pack(fill=tk.X, pady=(0,20))
        
        author_content = tk.Frame(author_frame, bg='white', padx=15, pady=15)
        author_content.pack(fill=tk.X)
        
        author_label = tk.Label(author_content,
                              text="Developer Contact",
                              font=("Arial", 12, "bold"),
                              fg='#2c3e50',
                              bg='white')
        author_label.pack(anchor='w')
        
        contact_text = "Kenneth Tang\<EMAIL>"
        contact_label = tk.Label(author_content,
                               text=contact_text,
                               font=("Arial", 10),
                               fg='#34495e',
                               bg='white',
                               justify=tk.LEFT)
        contact_label.pack(anchor='w', pady=(5,0))
        
        # 規則說明卡片
        rules_frame = tk.Frame(left_frame, bg='white', relief=tk.SOLID, borderwidth=1)
        rules_frame.pack(fill=tk.X)
        
        rules_content = tk.Frame(rules_frame, bg='white', padx=15, pady=15)
        rules_content.pack(fill=tk.X)
        
        rules_title = tk.Label(rules_content,
                             text="Available Column Rules",
                             font=("Arial", 12, "bold"),
                             fg='#2c3e50',
                             bg='white')
        rules_title.pack(anchor='w')
        
        rules_text = """1. Percentage Discount
   • Input: "%" symbol
   • Display: Calculated percentage (e.g., "25% OFF")

2. Custom Text
   • Input: Any text (e.g., "NEW ARRIVAL")
   • Display: Shows exactly as entered

3. Numeric Value
   • Input: Any number
   • Display: Uses default special text from settings

4. Empty Cell
   • Display: Uses default special text from settings"""
        
        rules_label = tk.Label(rules_content,
                             text=rules_text,
                             font=("Arial", 10),
                             fg='#34495e',
                             bg='white',
                             justify=tk.LEFT)
        rules_label.pack(anchor='w', pady=(5,0))
        
        # 右側示例圖片區域
        right_frame = tk.Frame(content_frame, bg='white')
        right_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10,0))
        
        # 定義 start_import 函數（移到按鈕創建之前）
        def start_import():
            selected_file = selected_setting.get()
            if not selected_file:
                show_error("Please select a settings file")
                return
            
            global settings
            settings = load_selected_settings(selected_file)
            
            # 在這裡註冊字體，使用選擇的設定檔中的字體路徑
            try:
                pdfmetrics.registerFont(TTFont('LuckiestGuy', settings['FONT_PATH']))
                pdfmetrics.registerFont(TTFont('Arial', settings['ARIAL_FONT_PATH']))
            except Exception as e:
                show_error(f"Error registering fonts: {str(e)}")
                return
            
            root.quit()
            root.destroy()
            generate_labels(selected_file)  # 傳遞選擇的設定檔名稱
        
        # 創建一個框架來包含所有按鈕
        buttons_frame = tk.Frame(right_frame, bg='white')
        buttons_frame.pack(pady=(0,20))
        
        # 上傳按鈕
        upload_button = tk.Button(buttons_frame,
                                text="Upload Excel File",
                                command=start_import,
                                font=("Arial", 12, "bold"),  # 加粗字體
                                bg='#2ecc71',  # 時尚的綠色
                                fg='white',
                                relief=tk.FLAT,
                                cursor='hand2',
                                width=20,        # 設置按鈕寬度（以字符為單位）
                                height=2,        # 設置按鈕高度（以字符為單位）
                                padx=70,         # 水平內邊距（像素）
                                pady=15)         # 垂直內邊距（像素）
        upload_button.pack()
        
        # 按鈕懸停效果
        def on_enter(e):
            upload_button['bg'] = '#27ae60'  # 懸停時的深綠色
        
        def on_leave(e):
            upload_button['bg'] = '#2ecc71'  # 恢復原來的綠色
        
        upload_button.bind("<Enter>", on_enter)
        upload_button.bind("<Leave>", on_leave)
        
        # 添加 Fred 導出指南按鈕
        def open_fred():
            root.destroy()
            open_fred_guide()
            
        fred_guide_button = tk.Button(buttons_frame,
                                    text="How to export data from Fred",
                                    command=open_fred,
                                    font=("Arial", 10),
                                    bg='#3498db',
                                    fg='white',
                                    relief=tk.FLAT,
                                    cursor='hand2',
                                    width=25,
                                    pady=5)
        fred_guide_button.pack(pady=(10,5))
        
        # 添加新按鈕
        def open_rules():
            root.destroy()
            open_label_rules()
            
        rules_button = tk.Button(buttons_frame,
                               text="Label display rules",
                               command=open_rules,
                               font=("Arial", 10),
                               bg='#3498db',
                               fg='white',
                               relief=tk.FLAT,
                               cursor='hand2',
                               width=25,
                               pady=5)
        rules_button.pack()
        
        # 兩個按鈕的懸停效果
        def on_enter_fred(e):
            fred_guide_button['bg'] = '#2980b9'
        
        def on_leave_fred(e):
            fred_guide_button['bg'] = '#3498db'
            
        def on_enter_rules(e):
            rules_button['bg'] = '#2980b9'
        
        def on_leave_rules(e):
            rules_button['bg'] = '#3498db'
        
        fred_guide_button.bind("<Enter>", on_enter_fred)
        fred_guide_button.bind("<Leave>", on_leave_fred)
        rules_button.bind("<Enter>", on_enter_rules)
        rules_button.bind("<Leave>", on_leave_rules)
        
        # 載入並顯示示例圖片
        try:
            image_path = get_resource_path('example.png')
            if os.path.exists(image_path):
                image = Image.open(image_path)
                max_size = (250, 250)  # 縮小示例圖片
                image.thumbnail(max_size, Image.Resampling.LANCZOS)
                photo = ImageTk.PhotoImage(image)
                
                image_frame = tk.Frame(right_frame, relief=tk.SOLID, borderwidth=1, bg='white')
                image_frame.pack()
                
                image_label = tk.Label(image_frame, image=photo, bg='white')
                image_label.image = photo
                image_label.pack(padx=2, pady=2)
                
        except Exception as e:
            print(f"Warning: Could not load example image: {str(e)}")
        
        # Label Settings 框架
        settings_frame = tk.LabelFrame(right_frame,
                                     text=" Label Settings ",
                                     font=("Arial", 12, "bold"),
                                     bg='white',
                                     fg='#2c3e50',
                                     width=180,
                                     height=130)
        settings_frame.pack(fill=tk.X, pady=(0,10), padx=20)
        settings_frame.pack_propagate(False)
        
        # 設定選擇的容器
        settings_container = tk.Frame(settings_frame, bg='white')
        settings_container.pack(padx=20, pady=20, fill=tk.X)  # 增加容器內邊距
        
        # 下拉選單標籤和選單在同一行
        settings_label = tk.Label(settings_container,
                                text="Select a profile:",
                                font=("Arial", 10),
                                fg='#2c3e50',
                                bg='white')
        settings_label.pack(side=tk.LEFT, padx=(0,10))
        
        # 獲取設定檔列表
        setting_files = get_setting_files()
        selected_setting = tk.StringVar(root)
        selected_setting.set('label_settings.txt')
        
        # 創建下拉選單
        settings_menu = ttk.Combobox(settings_container,
                                   textvariable=selected_setting,
                                   values=setting_files,
                                   font=("Arial", 10),
                                   width=30,
                                   state="readonly")
        settings_menu.pack(side=tk.LEFT)
        
        # 提示文字
        hint_label = tk.Label(settings_frame,
                            text="* Choose a setting profile before importing Excel file",
                            font=("Arial", 9),
                            fg='#7f8c8d',
                            bg='white')
        hint_label.pack(pady=(0,10), padx=15, anchor='w')
        
        # 視窗設置
        width = 800
        height = 600  # 減少視窗高度
        x = (root.winfo_screenwidth() - width) // 2
        y = (root.winfo_screenheight() - height) // 2
        root.geometry(f"{width}x{height}+{x}+{y}")
        root.resizable(False, False)
        
        root.mainloop()
        
    except Exception as e:
        show_error(f"Error occurred: {str(e)}")
        sys.exit(1)

def generate_labels(selected_file):
    try:
    excel_path = select_excel_file()
    if not excel_path:
            sys.exit(0)
            
        # 使用新載入的設定更新所有全域變數
        global COLS, ROWS, MARGIN_X, MARGIN_Y, LABEL_WIDTH, LABEL_HEIGHT
        global LINE_MARGIN, LINE_THICKNESS, LINE_Y_POSITION
        global TEXT_SPACING, SPECIAL_TEXT, SPECIAL_MARGIN, SPECIAL_Y_POSITION
        global SPECIAL_BACKGROUND_PADDING, SPECIAL_MAX_FONT_SIZE, SPECIAL_MIN_FONT_SIZE
        global ITEM_DESC_FONT_SIZE, ITEM_DESC_MAX_LENGTH, ITEM_DESC_CHARS_PER_LINE
        global ITEM_DESC_Y_POSITION, ITEM_DESC_Y_POSITION_SINGLE, ITEM_DESC_LINE_SPACING
        global RETAIL_FONT_SIZE, RETAIL_Y_POSITION, RETAIL_LINE_SPACING
        global SAVE_FONT_SIZE, SAVE_Y_POSITION, VALID_DATE_Y_POSITION, VALID_DATE_FONT_SIZE
        global TEXT_SALE_PRICE_FONT_SIZE, TEXT_SALE_PRICE_Y_POSITION, TEXT_SALE_PRICE_LINE_SPACING
        global ITEM_CODE_FONT_SIZE, ITEM_CODE_X_POSITION, ITEM_CODE_Y_POSITION, ITEM_CODE_FONT
        global SALE_PRICE_FONT_SIZE, SALE_PRICE_Y_POSITION, SALE_PRICE_LINE_SPACING
        
        # 更新基本設定
        COLS = int(settings['COLS'])
        ROWS = int(settings['ROWS'])
        MARGIN_X = settings['MARGIN_X'] * mm
        MARGIN_Y = settings['MARGIN_Y'] * mm
        LABEL_WIDTH = (PAGE_WIDTH - 2 * MARGIN_X) / COLS
        LABEL_HEIGHT = (PAGE_HEIGHT - 2 * MARGIN_Y) / ROWS
        
        # 更新線條設定
        LINE_MARGIN = settings['LINE_MARGIN'] * mm
        LINE_THICKNESS = settings['LINE_THICKNESS']
        LINE_Y_POSITION = settings['LINE_Y_POSITION'] * mm
        
        # 更新文字設定
        TEXT_SPACING = settings['TEXT_SPACING'] * mm
        SPECIAL_TEXT = settings['SPECIAL_TEXT']
        SPECIAL_MARGIN = settings['SPECIAL_MARGIN'] * mm
        SPECIAL_Y_POSITION = settings['SPECIAL_Y_POSITION'] * mm
        SPECIAL_BACKGROUND_PADDING = settings['SPECIAL_BACKGROUND_PADDING'] * mm
        SPECIAL_MAX_FONT_SIZE = int(settings['SPECIAL_MAX_FONT_SIZE'])
        SPECIAL_MIN_FONT_SIZE = int(settings['SPECIAL_MIN_FONT_SIZE'])
        
        # 更新商品描述設定
        ITEM_DESC_FONT_SIZE = int(settings['ITEM_DESC_FONT_SIZE'])
        ITEM_DESC_MAX_LENGTH = int(settings['ITEM_DESC_MAX_LENGTH'])
        ITEM_DESC_CHARS_PER_LINE = int(settings['ITEM_DESC_CHARS_PER_LINE'])
        ITEM_DESC_Y_POSITION = settings['ITEM_DESC_Y_POSITION'] * mm
        ITEM_DESC_Y_POSITION_SINGLE = settings['ITEM_DESC_Y_POSITION_SINGLE'] * mm
        ITEM_DESC_LINE_SPACING = settings['ITEM_DESC_LINE_SPACING'] * mm
        
        # 更新價格設定
        RETAIL_FONT_SIZE = int(settings.get('RRP_FONT_SIZE'))
        RETAIL_Y_POSITION = settings.get('RRP_Y_POSITION') * mm
        RETAIL_LINE_SPACING = settings.get('RRP_LINE_SPACING') * mm
        SAVE_FONT_SIZE = int(settings['SAVE_FONT_SIZE'])
        SAVE_Y_POSITION = settings['SAVE_Y_POSITION'] * mm
        VALID_DATE_Y_POSITION = settings['VALID_DATE_Y_POSITION'] * mm
        VALID_DATE_FONT_SIZE = int(settings['VALID_DATE_FONT_SIZE'])
        
        # 更新文字售價設定
        TEXT_SALE_PRICE_FONT_SIZE = int(settings.get('TEXT_SALE_PRICE_FONT_SIZE', RETAIL_FONT_SIZE))
        TEXT_SALE_PRICE_Y_POSITION = settings.get('TEXT_SALE_PRICE_Y_POSITION', RETAIL_Y_POSITION/mm) * mm
        TEXT_SALE_PRICE_LINE_SPACING = settings.get('TEXT_SALE_PRICE_LINE_SPACING', RETAIL_LINE_SPACING/mm) * mm
        
        # 更新商品代碼設定
        ITEM_CODE_FONT_SIZE = int(settings.get('ITEM_CODE_FONT_SIZE', 8))
        ITEM_CODE_X_POSITION = settings.get('ITEM_CODE_X_POSITION', 5) * mm
        ITEM_CODE_Y_POSITION = settings.get('ITEM_CODE_Y_POSITION', 5) * mm
        ITEM_CODE_FONT = settings.get('ITEM_CODE_FONT', 'Arial')
        
        # 更新售價設定
        SALE_PRICE_FONT_SIZE = int(settings['SALE_PRICE_FONT_SIZE'])
        SALE_PRICE_Y_POSITION = settings['SALE_PRICE_Y_POSITION'] * mm
        SALE_PRICE_LINE_SPACING = settings.get('SALE_PRICE_LINE_SPACING', RETAIL_LINE_SPACING) * mm

    df = pd.read_excel(excel_path)
        required_columns = ['Item Description', 'Sale Price', 'Retail', 'Item Code', 'Available']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            show_error(f"Excel file is missing required columns:\n{', '.join(missing_columns)}")
            sys.exit(1)

        # 檢查是否有 Valid Date 欄位
        has_valid_date = 'Valid Date' in df.columns
        if not has_valid_date:
            valid_date = get_valid_date()
            if valid_date:  # 如果用戶輸入了日期
                df['Valid Date'] = valid_date  # 直接使用日期選擇器返回的格式
            else:  # 如果用戶留空或取消
                df['Valid Date'] = None  # 設置為 None，這樣在繪製時會跳過有效期限

        # 移除 Valid Date 欄位中的空值，但保留其他欄位
        df = df[required_columns + (['Valid Date'] if has_valid_date or 'Valid Date' in df.columns else [])]
        
        # 檢查輸出模式
        is_thermal = settings.get('THERMAL_PAPER', 'No').strip('"').lower() == 'yes'
        
        if is_thermal:
            # 創建輸出目錄
            output_dir = os.path.join(os.path.dirname(excel_path), 'labels')
            os.makedirs(output_dir, exist_ok=True)
            
            # 為每個商品生成單獨的 PNG
            for i, row in df.iterrows():
                filename = draw_label_png(
                    row['Item Description'], row['Sale Price'], row['Retail'],
                    row.get('Valid Date'), row['Item Code'], row['Available'],
                    output_dir
                )
                print(f"Created label: {filename}")
            
            root = tk.Tk()
            root.withdraw()
            messagebox.showinfo("Success", f"All PNG labels have been generated in:\n{output_dir}")
            root.destroy()
            sys.exit(0)  # 成功完成後退出
        else:
            # 生成 PDF
            excel_filename = os.path.splitext(os.path.basename(excel_path))[0]
            
            # 獲取設定檔名稱並移除 "Label_setting" 部分和 ".txt" 副檔名
            setting_name = selected_file.lower()
            setting_name = setting_name.replace('label_settings', '')
            setting_name = setting_name.replace('label_setting', '')
            setting_name = setting_name.replace('.txt', '')
            setting_name = setting_name.strip('_')
            
            if setting_name:
                output_filename = f"{excel_filename}_{setting_name}.pdf"
            else:
                output_filename = f"{excel_filename}.pdf"
            
            output_path = os.path.join(os.path.dirname(excel_path), output_filename)
    c = canvas.Canvas(output_path, pagesize=A4)

    for i, row in df.iterrows():
        index_in_page = i % (COLS * ROWS)
        col = index_in_page % COLS
        row_pos = index_in_page // COLS

        x = MARGIN_X + col * LABEL_WIDTH
        y = PAGE_HEIGHT - MARGIN_Y - (row_pos + 1) * LABEL_HEIGHT

        if index_in_page == 0 and i != 0:
            c.showPage()

                draw_label(c, x, y, row['Item Description'], row['Sale Price'], row['Retail'],
                          row.get('Valid Date'), row['Item Code'], row['Available'])

    c.save()
            root = tk.Tk()
            root.withdraw()
            messagebox.showinfo("Success", f"PDF has been generated successfully at:\n{output_path}")
            root.destroy()
            sys.exit(0)

    except Exception as e:
        if isinstance(e, PermissionError) and e.errno == 13:
            show_error("Cannot create output file - The file may be in use.\nPlease close the file and restart the application.")
        else:
            show_error(f"Error occurred: {str(e)}")
        sys.exit(1)

def get_setting_files():
    """獲取所有 label_setting 開頭的設定檔"""
    app_path = get_app_path()
    setting_files = []
    default_setting = 'label_settings.txt'
    
    try:
        # 首先檢查預設設定檔是否存在
        if os.path.exists(os.path.join(app_path, default_setting)):
            setting_files.append(default_setting)  # 將預設檔案添加到列表開頭
        
        # 掃描目錄中的其他設定檔
        for file in os.listdir(app_path):
            if (file.lower().startswith('label_setting') and 
                file.lower().endswith('.txt') and 
                file != default_setting):  # 排除預設檔案，因為已經添加過了
                setting_files.append(file)
        
        # 如果完全沒有找到任何設定檔（包括預設檔）
        if not setting_files:
            show_error(f"No settings file found. Please ensure {default_setting} exists in the application directory.")
            sys.exit(1)
        
        # 對預設檔案之後的檔案進行排序
        if len(setting_files) > 1:
            other_files = setting_files[1:]
            other_files.sort()
            setting_files = [setting_files[0]] + other_files
        
    except Exception as e:
        print(f"Error reading directory: {str(e)}")
        if os.path.exists(os.path.join(app_path, default_setting)):
            setting_files = [default_setting]
        else:
            show_error(f"Error: Cannot find {default_setting}")
            sys.exit(1)
    
    return setting_files

def load_selected_settings(selected_file):
    """載入選擇的設定檔"""
    app_path = get_app_path()
    settings_path = os.path.join(app_path, selected_file)
    
    settings = {}
    try:
        with open(settings_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    key, value = line.split('=')
                    key = key.strip()
                    value = value.strip()
                    if value.startswith('"') and value.endswith('"'):
                        settings[key] = value[1:-1]
                    else:
                        try:
                            settings[key] = float(value)
                        except ValueError:
                            settings[key] = value
        
        # 檢查字體文件是否存在
        for font_key in ['FONT_PATH', 'ARIAL_FONT_PATH']:
            if font_key in settings:
                font_path = settings[font_key]
                possible_paths = [
                    os.path.join(app_path, font_path),
                    os.path.join(app_path, os.path.basename(font_path)),
                ]
                
                for path in possible_paths:
                    if os.path.isfile(path):
                        settings[font_key] = path
                        break
                else:
                    show_error(f"Font file not found: {font_path}\nPlease ensure the font file is in the same directory as the application.")
                    sys.exit(1)
        
        return settings
    
    except Exception as e:
        show_error(f"Error reading settings file: {str(e)}")
        sys.exit(1)

# 執行
if __name__ == "__main__":
    create_labels()