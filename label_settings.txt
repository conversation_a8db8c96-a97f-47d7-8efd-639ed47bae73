
# POSITION number Smaller = lower

# (Page Settings)
MARGIN_X = 5
MARGIN_Y = 5
COLS = 3
ROWS = 3

# (Line Settings)
LINE_MARGIN = 2
LINE_THICKNESS = 0.5
LINE_Y_POSITION = 80

# (Promotion Text Settings)
TEXT_SPACING = 2
SPECIAL_TEXT = "    SPECIAL   "
SPECIAL_MARGIN = 2
SPECIAL_Y_POSITION = 70
SPECIAL_BACKGROUND_PADDING = 2
SPECIAL_MAX_FONT_SIZE = 35
SPECIAL_MIN_FONT_SIZE = 16

#  (Item Description Settings)
ITEM_DESC_FONT_SIZE = 15
ITEM_DESC_MAX_LENGTH = 120
ITEM_DESC_CHARS_PER_LINE = 50
ITEM_DESC_Y_POSITION = 60
ITEM_DESC_Y_POSITION_SINGLE = 55
ITEM_DESC_LINE_SPACING = 1

# (Price Settings)
SALE_PRICE_FONT_SIZE = 55
SALE_PRICE_Y_POSITION = 30
SALE_PRICE_LINE_SPACING = 2

# (Sale Price = Text  Settings)
TEXT_SALE_PRICE_FONT_SIZE = 35
TEXT_SALE_PRICE_Y_POSITION = 35
TEXT_SALE_PRICE_LINE_SPACING = 0

#  (RRP Settings)
RRP_FONT_SIZE = 11
RRP_Y_POSITION = 9
RRP_LINE_SPACING = 2


# (Save how much Settings)
SAVE_FONT_SIZE = 20
SAVE_Y_POSITION = 20


# (SValid date Settings)

VALID_DATE_Y_POSITION = 4
VALID_DATE_FONT_SIZE = 10


# 商品代碼設定 (Item Code Settings)
ITEM_CODE_FONT_SIZE = 8
ITEM_CODE_X_POSITION = 2
ITEM_CODE_Y_POSITION = 2
ITEM_CODE_FONT = "Arial"



# 字體設定 (Font Settings)
FONT_PATH = "C:\\USERS\\<USER>\\APPDATA\\LOCAL\\MICROSOFT\\WINDOWS\\FONTS\\LUCKIESTGUY.TTF"
ARIAL_FONT_PATH = "C:\\WINDOWS\\FONTS\\ARIAL.TTF" 

THERMAL_PAPER = "No"