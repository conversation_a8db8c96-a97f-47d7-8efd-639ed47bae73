#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 Ethical 標籤條碼視覺效果變化
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ticket_generator'))

from utils.config_manager import ConfigManager
from core.label_generator import LabelGenerator
from models.data_models import ProductItem
from datetime import datetime

def test_ethical_barcode_visual_changes():
    """測試 Ethical 標籤條碼的視覺變化"""
    print("=== 測試 Ethical 標籤條碼視覺變化 ===\n")
    
    # 初始化組件
    config_manager = ConfigManager()
    label_generator = LabelGenerator(config_manager)
    
    # 創建測試用的 Ethical 商品
    ethical_item = ProductItem(
        item_code="ETH001",
        item_description="測試 Ethical 藥品",
        department="Ethicals",
        retail=45.99,
        sale_price=39.99,
        rrp=None,
        modified_date=datetime.now()
    )
    
    print(f"測試商品: {ethical_item.item_code} - {ethical_item.item_description}")
    print(f"部門: {ethical_item.department}")
    print()
    
    # 測試不同的條碼設置組合
    test_cases = [
        {
            'name': '預設設置',
            'position_offset_x': 0,
            'position_offset_y': 65,
            'width_scale': 0.8,
            'height_scale': 0.5
        },
        {
            'name': '條碼向右移動',
            'position_offset_x': 50,
            'position_offset_y': 65,
            'width_scale': 0.8,
            'height_scale': 0.5
        },
        {
            'name': '條碼向左移動',
            'position_offset_x': -50,
            'position_offset_y': 65,
            'width_scale': 0.8,
            'height_scale': 0.5
        },
        {
            'name': '條碼向上移動',
            'position_offset_x': 0,
            'position_offset_y': 30,
            'width_scale': 0.8,
            'height_scale': 0.5
        },
        {
            'name': '條碼向下移動',
            'position_offset_x': 0,
            'position_offset_y': 100,
            'width_scale': 0.8,
            'height_scale': 0.5
        },
        {
            'name': '條碼變寬',
            'position_offset_x': 0,
            'position_offset_y': 65,
            'width_scale': 1.2,
            'height_scale': 0.5
        },
        {
            'name': '條碼變窄',
            'position_offset_x': 0,
            'position_offset_y': 65,
            'width_scale': 0.5,
            'height_scale': 0.5
        },
        {
            'name': '條碼變高',
            'position_offset_x': 0,
            'position_offset_y': 65,
            'width_scale': 0.8,
            'height_scale': 0.8
        },
        {
            'name': '條碼變矮',
            'position_offset_x': 0,
            'position_offset_y': 65,
            'width_scale': 0.8,
            'height_scale': 0.3
        },
        {
            'name': '組合測試_右上角小條碼',
            'position_offset_x': 80,
            'position_offset_y': 20,
            'width_scale': 0.6,
            'height_scale': 0.4
        }
    ]
    
    print("開始生成不同設置的標籤...")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"{i:2d}. 測試: {test_case['name']}")
        print(f"    X位置: {test_case['position_offset_x']:3d}")
        print(f"    Y位置: {test_case['position_offset_y']:3d}")
        print(f"    寬度比例: {test_case['width_scale']:.1f}")
        print(f"    高度比例: {test_case['height_scale']:.1f}")
        
        try:
            # 更新配置
            config_manager.update_config('barcode_options_ethicals', 'position_offset_x', test_case['position_offset_x'])
            config_manager.update_config('barcode_options_ethicals', 'position_offset_y', test_case['position_offset_y'])
            config_manager.update_config('barcode_options_ethicals', 'width_scale', test_case['width_scale'])
            config_manager.update_config('barcode_options_ethicals', 'height_scale', test_case['height_scale'])
            config_manager.reload_config()
            
            # 生成標籤
            image = label_generator.create_label(ethical_item)
            output_path = f"ethical_visual_test_{i:02d}_{test_case['name'].replace(' ', '_').replace('_', '')}.png"
            image.save(output_path)
            print(f"    ✓ 已生成: {output_path}")
            
        except Exception as e:
            print(f"    ✗ 錯誤: {str(e)}")
        
        print()
    
    # 驗證最終配置
    print("--- 驗證最終配置 ---")
    settings = config_manager.get_ethicals_settings()
    barcode_opts = settings['barcode_options']
    
    print("Ethical 條碼最終設置:")
    print(f"  X位置偏移: {barcode_opts['position_offset_x']}")
    print(f"  Y位置偏移: {barcode_opts['position_offset_y']}")
    print(f"  寬度比例: {barcode_opts['width_scale']}")
    print(f"  高度比例: {barcode_opts['height_scale']}")
    
    print("\n=== 測試完成 ===")
    print("✅ 已生成多個測試標籤，請檢查視覺效果")
    print("📋 測試項目:")
    print("  • 條碼 X 位置變化")
    print("  • 條碼 Y 位置變化")
    print("  • 條碼寬度變化")
    print("  • 條碼高度變化")
    print("  • 組合效果測試")
    
    print("\n💡 使用方法:")
    print("1. 查看生成的 PNG 文件")
    print("2. 比較不同設置的視覺效果")
    print("3. 確認條碼位置和大小確實有變化")

if __name__ == "__main__":
    test_ethical_barcode_visual_changes()
