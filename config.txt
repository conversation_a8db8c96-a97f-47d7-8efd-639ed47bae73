[file_paths]
excel_directory = C:\Users\<USER>\Documents\Lee\label\Price_Ticket-and-Promotion-Label\Excel

[font_sizes]
item_description = 30
retail = 100
item_code = 30
rrp = 20
date = 15

[text_wrap]
width = 15

[text_offsets]
item_description_offset_x = 10
item_description_offset_y = 200
retail_offset_y = 80
rrp_offset_x = -20
rrp_offset_y = -280
date_offset_x = 20
date_offset_y = -65

[barcode_options]
position_offset_x = 0
position_offset_y = -14
width_scale = 0.8
height_scale = 0.5
module_width = 0.2
module_height = 15.0
quiet_zone = 6.0
write_text = True
resize_width_factor = 1.0
resize_height_factor = 1.0

[font_sizes_ethicals]
item_description = 50
retail = 50
item_code = 30
rrp = 20
date = 15

[text_wrap_ethicals]
width = 13

[text_offsets_ethicals]
item_description_offset_x = 10
item_description_offset_y = 30
retail_offset_y = 80
rrp_offset_x = -20
rrp_offset_y = -280
date_offset_x = 20
date_offset_y = -65

[barcode_options_ethicals]
position_offset_x = 0
position_offset_y = 65
width_scale = 0.8
height_scale = 0.5
module_width = 0.2
module_height = 15.0
quiet_zone = 6.0
write_text = True
resize_width_factor = 1.0
resize_height_factor = 1.0

[Settings]
template_folder = ./templates
output_folder = ./output
date_format = %%d/%%m/%%Y
font_name = arialbd.ttf
normal_template = normal_template.txt
ethicals_template = ethicals_template.txt
excel_folder = ./excel
printer_name = Gprinter GP-1134T

