from PIL import Image, ImageDraw, ImageFont
from datetime import datetime
import barcode
from barcode.writer import ImageWriter
import textwrap
import win32print
import win32ui
from PIL import ImageWin
import requests
from io import BytesIO
import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import platform
import sys
import warnings
from openpyxl import load_workbook
import random
import io
import pandas as pd
import configparser
import glob
from barcode import Code128
from PIL import ImageTk
import traceback
from tkcalendar import DateEntry  # 添加這行在文件開頭的 import 區域
import time

# Ignore specific UserWarning from openpyxl
warnings.filterwarnings("ignore", category=UserWarning, module="openpyxl")

def resource_path(relative_path):
    """ Get absolute path to resource, works for dev and for PyInstaller """
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")

    return os.path.join(base_path, relative_path)

# 在程式開始時就檢查資源是否可用
def check_resources():
    font_path = resource_path("arialbd.ttf")
    logo_path = resource_path("logo.jpg")
    
    if not os.path.exists(font_path):
        print(f"Warning: Font file not found at {font_path}")
    if not os.path.exists(logo_path):
        print(f"Warning: Logo file not found at {logo_path}")

# 然後在程式中使用這個函數來訪問檔案
font_path = resource_path("arialbd.ttf")
logo_path = resource_path("logo.jpg")

def draw_text_centered(draw, text, font, image_width, offset_y=0):
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_x = (image_width - text_width) / 2
    draw.text((text_x, offset_y), text, font=font, fill="black")

def wrap_text_to_fit(draw, text, font, max_width, department):
    """Wrap text to fit within a specified width."""
    config = load_config('config.txt')
    
    # 根據部門選擇文字換行寬度
    if department.lower() == "ethicals":
        wrap_width = int(config['text_wrap_ethicals']['width'])
    else:
        wrap_width = int(config['text_wrap']['width'])
    
    lines = textwrap.wrap(text, width=wrap_width)
    wrapped_text = []
    for line in lines:
        while True:
            bbox = draw.textbbox((0, 0), line, font=font)
            line_width = bbox[2] - bbox[0]
            if line_width <= max_width or len(line) == 0:
                break
            line = line[:-1]
        wrapped_text.append(line)
    return wrapped_text

def load_config(file_path):
    config = configparser.ConfigParser()
    config.read(file_path)
    return config

def create_image(item_code, item_description, retail, rrp, modified_date, excel_file_path, department):
    # 確保 department 的值被正確使用
    print(f"Department from Excel: {department}")  # 調試信息

    # 每次生成標籤時重新加載配置
    config = load_config('config.txt')
    
    # 設定圖像尺寸（以像素為單位）
    width_px = int(50 / 25.4 * 300)  # 50mm 寬
    height_px = int(30 / 25.4 * 300)  # 30mm 高

    # 創建一個白色背景的圖像
    image = Image.new('RGB', (width_px, height_px), 'white')
    draw = ImageDraw.Draw(image)

    # 根據部門選擇設置
    if department.strip().lower() == "ethicals":
        print("Using Ethicals settings")  # 調試信息
        font_sizes = {
            'item_description': int(config['font_sizes_ethicals']['item_description']),
            'retail': int(config['font_sizes_ethicals']['retail']),
            'item_code': int(config['font_sizes_ethicals']['item_code']),
            'rrp': int(config['font_sizes_ethicals']['rrp']),
            'date': int(config['font_sizes_ethicals']['date'])
        }
        
        text_offsets = {
            'item_description': {'offset_x': int(config['text_offsets_ethicals']['item_description_offset_x']), 'offset_y': int(config['text_offsets_ethicals']['item_description_offset_y'])},
            'retail': {'offset_x': int(config['text_offsets_ethicals']['retail_offset_x']), 'offset_y': int(config['text_offsets_ethicals']['retail_offset_y'])},
            'item_code': {'offset_x': int(config['text_offsets_ethicals']['item_code_offset_x']), 'offset_y': int(config['text_offsets_ethicals']['item_code_offset_y'])},
            'rrp': {'offset_x': int(config['text_offsets_ethicals']['rrp_offset_x']), 'offset_y': int(config['text_offsets_ethicals']['rrp_offset_y'])},
            'date': {'offset_x': int(config['text_offsets_ethicals']['date_offset_x']), 'offset_y': int(config['text_offsets_ethicals']['date_offset_y'])}
        }

        barcode_options = {
            'module_width': float(config['barcode_options_ethicals']['module_width']),
            'module_height': float(config['barcode_options_ethicals']['module_height']),
            'quiet_zone': float(config['barcode_options_ethicals']['quiet_zone']),
            'write_text': config['barcode_options_ethicals'].getboolean('write_text'),
            'resize_width_factor': float(config['barcode_options_ethicals']['resize_width_factor']),
            'resize_height_factor': float(config['barcode_options_ethicals']['resize_height_factor']),
            'position_offset_y': int(config['barcode_options_ethicals']['position_offset_y'])
        }
    else:
        print("Using non-Ethicals settings")  # 調試信息
        font_sizes = {
            'item_description': int(config['font_sizes']['item_description']),
            'retail': int(config['font_sizes']['retail']),
            'item_code': int(config['font_sizes']['item_code']),
            'rrp': int(config['font_sizes']['rrp']),
            'date': int(config['font_sizes']['date'])
        }
        
        text_offsets = {
            'item_description': {'offset_x': int(config['text_offsets']['item_description_offset_x']), 'offset_y': int(config['text_offsets']['item_description_offset_y'])},
            'retail': {'offset_x': int(config['text_offsets']['retail_offset_x']), 'offset_y': int(config['text_offsets']['retail_offset_y'])},
            'item_code': {'offset_x': int(config['text_offsets']['item_code_offset_x']), 'offset_y': int(config['text_offsets']['item_code_offset_y'])},
            'rrp': {'offset_x': int(config['text_offsets']['rrp_offset_x']), 'offset_y': int(config['text_offsets']['rrp_offset_y'])},
            'date': {'offset_x': int(config['text_offsets']['date_offset_x']), 'offset_y': int(config['text_offsets']['date_offset_y'])}
        }

        barcode_options = {
            'module_width': float(config['barcode_options']['module_width']),
            'module_height': float(config['barcode_options']['module_height']),
            'quiet_zone': float(config['barcode_options']['quiet_zone']),
            'write_text': config['barcode_options'].getboolean('write_text'),
            'resize_width_factor': float(config['barcode_options']['resize_width_factor']),
            'resize_height_factor': float(config['barcode_options']['resize_height_factor']),
            'position_offset_y': int(config['barcode_options']['position_offset_y'])
        }

    # 生成標籤時檢查字體大小
    if font_sizes['item_description'] > 0:
        # 繪製 item_description
        print(f"Drawing item_description with font size {font_sizes['item_description']}")
        font = ImageFont.truetype(font_path, font_sizes['item_description'])
        max_text_width = width_px - 20  # 留出一些邊距
        wrapped_text = wrap_text_to_fit(draw, item_description, font, max_text_width, department)
        current_y = text_offsets['item_description']['offset_y']
        for line in wrapped_text:
            draw_text_centered(draw, line, font, width_px, offset_y=current_y)
            current_y += font_sizes['item_description']

    if font_sizes['retail'] > 0:
        # 繪製 retail
        font = ImageFont.truetype(font_path, font_sizes['retail'])
        formatted_retail = f"${retail:.2f}"
        draw_text_centered(draw, formatted_retail, font, width_px, offset_y=text_offsets['retail']['offset_y'])

    if font_sizes['item_code'] > 0:
        # 繪製 item_code
        print(f"Drawing item_code with font size {font_sizes['item_code']}")
        font = ImageFont.truetype(font_path, font_sizes['item_code'])
        draw.text((text_offsets['item_code']['offset_x'], text_offsets['item_code']['offset_y']), item_code, font=font, fill="black")

    if font_sizes['rrp'] > 0:
        # 繪製 rrp
        font = ImageFont.truetype(font_path, font_sizes['rrp'])
        
        # 清理並轉換價格
        def clean_price(price):
            if isinstance(price, (int, float)):
                return float(price)
            if isinstance(price, str):
                return float(price.replace('$', '').replace(',', '').strip())
            return 0.0

        # 確保 RRP 和零售價是數字類型
        clean_rrp = clean_price(rrp)
        clean_retail = clean_price(retail)
        
        # 只有當 RRP 大於零售價時才顯示
        if clean_rrp > clean_retail:
            rrp_label = f"RRP: ${clean_rrp:.2f}"
            rrp_bbox = draw.textbbox((0, 0), rrp_label, font=font)
            draw.text(
                (width_px - rrp_bbox[2] + text_offsets['rrp']['offset_x'], 
                height_px - rrp_bbox[3] + text_offsets['rrp']['offset_y']), 
                rrp_label, 
                font=font, 
                fill="black"
            )

    if font_sizes['date'] > 0:
        # 繪製 date
        print(f"Drawing date with font size {font_sizes['date']}")
        font = ImageFont.truetype(font_path, font_sizes['date'])
        combined_date = f"{modified_date}                                 {modified_date[-4:]}"
        draw.text((text_offsets['date']['offset_x'], height_px + text_offsets['date']['offset_y']), combined_date, font=font, fill="black")

    # 生成條形碼，使用 Code128
    code128 = barcode.get('code128', item_code, writer=ImageWriter())
    barcode_image = code128.render(writer_options=barcode_options)

    # 調整條形碼大小
    barcode_width, barcode_height = barcode_image.size
    barcode_image = barcode_image.resize(
        (int(barcode_width * barcode_options['resize_width_factor']),
         int(barcode_height * barcode_options['resize_height_factor']))
    )

    # 將條形碼粘貼到主圖像上
    barcode_x = (width_px - barcode_image.width) // 2
    barcode_y = height_px // 2 + barcode_options['position_offset_y']
    image.paste(barcode_image, (barcode_x, barcode_y))

    # 保存圖像
    image_path = 'output_image_with_barcode.png'
    image.save(image_path)

    # 使用新的列印函數
    print_label_with_printer(image_path)

def select_excel_file():
    root = tk.Tk()
    root.withdraw()  # Hide main window
    file_path = filedialog.askopenfilename(
        title="Select Excel File",
        filetypes=[
            ("Excel files", "*.xlsx;*.xls"),
            ("All files", "*.*")
        ],
        initialdir=os.path.expanduser("~/Desktop")  # Default to desktop directory
    )
    return file_path

def create_black_label(barcode_number):
    """Create a black label with barcode"""
    try:
        # 設定圖片大小和背景
        width = 400
        height = 300
        
        try:
            import os
            current_dir = os.getcwd()
            
            # 生成條碼（使用 ImageWriter 並設定選項）
            from barcode.writer import ImageWriter
            writer = ImageWriter()
            writer.set_options({
                'module_width': 0.2,
                'module_height': 15.0,
                'quiet_zone': 6.0,
                'font_size': 10,
                'text_distance': 5.0,
                'write_text': True,  # 顯示文字
                'font_path': "arialbd.ttf"  # 指定字型路徑
            })
            
            # 檢查字型是否存在
            font_path = "C:/Windows/Fonts/arialbd.ttf"
            if not os.path.exists(font_path):
                print("Font not found, using default font.")
                font_path = None
            
            # 生成條碼
            EAN = barcode.get_barcode_class('code128')
            ean = EAN(str(barcode_number), writer=writer)
            
            # 直接生成條碼圖片
            barcode_image = ean.render()
            
            # 創建新的白色背景
            final_img = Image.new('RGB', (width, height), 'white')
            
            # 調整條碼大小
            barcode_image = barcode_image.resize((350, 100), Image.Resampling.LANCZOS)
            
            # 計算條碼位置使其置中
            x = (width - barcode_image.width) // 2
            y = (height - barcode_image.height) // 2
            
            # 貼上條碼
            final_img.paste(barcode_image, (x, y))
            
            # 儲存最終圖片
            label_path = os.path.join(current_dir, f'{barcode_number}.png')
            final_img.save(label_path)
            
            # 列印標籤
            # print_label_with_printer(label_path)
            print(f"Label created: {label_path}")
            
        except Exception as e:
            print(f"Error generating barcode: {str(e)}")
            import traceback
            print(traceback.format_exc())
            
    except Exception as e:
        print(f"Error creating label: {str(e)}")

def print_label(image_path, printer_name=None):
    # 打印圖像的統一邏輯
    try:
        print(f"Printer name: {printer_name}")  # 調試信息
        if platform.system() == "Windows":
            if printer_name:
                os.system(f'print /d:"{printer_name}" {image_path}')
            else:
                os.startfile(image_path, "print")
        elif platform.system() == "Darwin":  # macOS
            os.system(f"lp -d {printer_name} {image_path}" if printer_name else f"lp {image_path}")
        else:  # Linux
            os.system(f"lpr -P {printer_name} {image_path}" if printer_name else f"lpr {image_path}")
    except Exception as e:
        print(f"Error printing label: {e}")

def create_searchable_label(barcode_number, excel_file_path):
    """Create a searchable label with barcode and last modified date."""
    try:
        # 設定圖片大小和背景
        width = 400
        height = 300
        img = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(img)
        
        try:
            import os
            current_dir = os.getcwd()
            
            # 生成條碼（使用 ImageWriter 並設定選項）
            from barcode.writer import ImageWriter
            writer = ImageWriter()
            writer.set_options({
                'module_width': 0.2,
                'module_height': 15.0,
                'quiet_zone': 6.0,
                'font_size': 10,
                'text_distance': 5.0,
                'write_text': True,  # 顯示文字
                'font_path': "arialbd.ttf"  # 指定字型路徑
            })
            
            # 生成條碼
            EAN = barcode.get_barcode_class('code128')
            ean = EAN(str(barcode_number), writer=writer)
            
            # 直接生成條碼圖片
            barcode_image = ean.render()
            
            # 創建新的白色背景
            final_img = Image.new('RGB', (width, height), 'white')
            
            # 調整條碼大小
            barcode_image = barcode_image.resize((350, 100), Image.Resampling.LANCZOS)
            
            # 計算條碼位置使其置中
            x = (width - barcode_image.width) // 2
            y = (height - barcode_image.height) // 2
            
            # 貼上條碼
            final_img.paste(barcode_image, (x, y))
            
            # 獲取 Excel 文件的最後修改日期
            last_modified_time = os.path.getmtime(excel_file_path)
            last_modified_date = datetime.fromtimestamp(last_modified_time).strftime('%Y-%m-%d %H:%M:%S')
            
            # 在右下角顯示最後修改日期
            font = ImageFont.truetype("arialbd.ttf", 10)
            text_width, text_height = draw.textsize(last_modified_date, font=font)
            text_x = width - text_width - 10
            text_y = height - text_height - 10
            draw.text((text_x, text_y), last_modified_date, font=font, fill="black")
            
            # 儲存最終圖片
            label_path = os.path.join(current_dir, f'{barcode_number}.png')
            final_img.save(label_path)
            
            # 列印標籤
            print_label_with_printer(label_path)
            print(f"Label created: {label_path}")
            
        except Exception as e:
            print(f"Error generating barcode: {str(e)}")
            import traceback
            print(traceback.format_exc())
            
    except Exception as e:
        print(f"Error creating label: {str(e)}")

def show_ascii_art():
    ascii_art = """
██╗░░░░░███████╗███████╗  ██████╗░██╗░░██╗░█████╗░██████╗░███╗░░░███╗░█████╗░░█████╗░██╗░░░██╗
██║░░░░░██╔════╝██╔════╝  ██╔══██╗██║░░██║██╔══██╗██╔══██╗████╗░████║██╔══██╗██╔══██╗╚██╗░██╔╝
██║░░░░░█████╗░░█████╗░░  ██████╔╝███████║███████║██████╔╝██╔████╔██║███████║██║░░╚═╝░╚████╔╝░
██║░░░░░██╔══╝░░██╔══╝░░  ██╔═══╝░██╔══██║██╔══██║██╔══██╗██║╚██╔╝██║██╔══██║██║░░██╗░░╚██╔╝░░
███████╗███████╗███████╗  ██║░░░░░██║░░██║██║░░██║██║░░██║██║░╚═╝░██║██║░░██║╚█████╔╝░░░██║░░░
╚══════╝╚══════╝╚══════╝  ╚═╝░░░░░╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░░░░╚═╝╚═╝░░╚═╝░╚════╝░░░░╚═╝░░░

██████╗░██████╗░██╗░█████╗░███████╗  ████████╗░█████╗░░██████╗░
██╔══██╗██╔══██╗██║██╔══██╗██╔════╝  ╚══██╔══╝██╔══██╗██╔════╝░
██████╔╝██████╔╝██║██║░░╚═╝█████╗░░  ░░░██║░░░███████║██║░░██╗░
██╔═══╝░██╔══██╗██║██║░░██╗██╔══╝░░  ░░░██║░░░██╔══██║██║░░╚██╗
██║░░░░░██║░░██║██║╚█████╔╝███████╗  ░░░██║░░░██║░░██║╚██████╔╝
╚═╝░░░░░╚═╝░░╚═╝╚═╝░╚════╝░╚══════╝  ░░░╚═╝░░░╚═╝░░╚═╝░╚═════╝░

░██████╗░███████╗███╗░░██╗███████╗██████╗░░█████╗░████████╗░█████╗░██████╗░
██╔════╝░██╔════╝████╗░██║██╔════╝██╔══██╗██╔══██╗╚══██╔══╝██╔══██╗██╔══██╗
██║░░██╗░█████╗░░██╔██╗██║█████╗░░██████╔╝███████║░░░██║░░░██║░░██║██████╔╝
██║░░╚██╗██╔══╝░░██║╚████║██╔══╝░░██╔══██╗██╔══██║░░░██║░░░██║░░██║██╔══██╗
╚██████╔╝███████╗██║░╚███║███████╗██║░░██║██║░░██║░░░██║░░░╚█████╔╝██║░░██║
░╚═════╝░╚══════╝╚═╝░░╚══╝╚══════╝╚═╝░░╚═╝╚═╝░░╚═╝░░░╚═╝░░░░╚════╝░╚═╝░░╚═╝

Author: Kenneth Tang
Email: <EMAIL>
    """
    print(ascii_art)

def clear_screen():
    os.system('cls' if os.name == 'nt' else 'clear')

def check_system_requirements():
    """檢查系統要求和相關文件"""
    issues = []
    excel_path = None
    
    # 檢查作業系統
    import platform
    system = platform.system()
    print(f"\nSystem Check Results:")
    print(f"Operating System: {system} {platform.version()}")
    if system not in ['Windows', 'Darwin', 'Linux']:
        issues.append(f"Unsupported operating system: {system}")

    # 檢查 Python 版本
    import sys
    python_version = sys.version
    print(f"Python Version: {python_version}")
    
    # 檢查必要的套件（移除 pandas 和 PIL）
    required_packages = {
        'win32print': 'win32print',
        'win32ui': 'win32ui',
        'openpyxl': 'openpyxl'
    }
    
    print("\nChecking Required Packages:")
    for package, module in required_packages.items():
        try:
            __import__(module)
            print(f"✓ {package} is installed")
        except ImportError:
            print(f"✗ {package} is missing")
            issues.append(f"Missing package: {package}")

    # 檢查必要的文件
    required_files = {
        'Font File': 'arialbd.ttf',
        'Logo File': 'logo.jpg'
    }
    
    print("\nChecking Required Files:")
    for file_type, filename in required_files.items():
        if os.path.exists(resource_path(filename)):
            print(f"✓ {file_type} ({filename}) found")
        else:
            print(f"✗ {file_type} ({filename}) is missing")
            issues.append(f"Missing file: {filename}")

    # 檢查打印機設定
    print("\nChecking Printer Settings:")
    try:
        printer_name = "Gprinter GP-1134T"
        printers = win32print.EnumPrinters(2)
        printer_found = any(printer[2] == printer_name for printer in printers)
        if printer_found:
            print(f"✓ Printer '{printer_name}' found")
        else:
            print(f"✗ Printer '{printer_name}' not found")
            print("Available printers:")
            for printer in printers:
                print(f"  - {printer[2]}")
            issues.append(f"Printer not found: {printer_name}")
    except Exception as e:
        print(f"✗ Error checking printer: {str(e)}")
        issues.append(f"Printer check error: {str(e)}")

    # 檢查 Excel 文件格式
    print("\nChecking Excel File:")
    try:
        # 使用配置文件中的目錄來自動選擇最新的 Excel 文件
        config = load_config('config.txt')
        excel_directory = config['file_paths'].get('excel_directory', None)
        if excel_directory:
            excel_path = get_latest_excel_file(excel_directory)
            if not excel_path:
                print("No valid Excel file found in the specified directory. Please select a file manually.")
                excel_path = select_excel_file()
        else:
            print("No directory specified in config. Please select an Excel file manually.")
            excel_path = select_excel_file()

        if excel_path:
            df = pd.read_excel(excel_path)
            
            # 檢查行數
            total_rows = len(df)
            print(f"\nTotal rows in Excel: {total_rows}")
            if total_rows < 33000:
                print("⚠️ Warning: Excel file contains less than 33,000 rows.")
                print("This might indicate that the file doesn't contain all items.")
                issues.append(f"Excel file might be incomplete: only {total_rows} rows found")
            else:
                print("✓ Excel file row count looks normal")

            # 檢查必要欄位
            required_columns = [
                'Item Type', 'Item Code', 'Department', 'Item Description', 
                'Available', 'Sale Price', 'Retail', 'RRP', 
                'Created', 'Modified', 'Alias'
            ]
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                print(f"✗ Missing columns in Excel file: {', '.join(missing_columns)}")
                issues.append(f"Excel missing columns: {', '.join(missing_columns)}")
            else:
                print("✓ Excel file format is correct")
                
            # 檢查日期格式
            try:
                df['Modified'] = pd.to_datetime(df['Modified'])
                print("✓ Date format in 'Modified' column is valid")
            except Exception as e:
                print(f"✗ Invalid date format in 'Modified' column: {str(e)}")
                issues.append("Invalid date format in Excel")
    except Exception as e:
        print(f"✗ Error reading Excel file: {str(e)}")
        issues.append(f"Excel read error: {str(e)}")

    # 顯示總結
    print("\nDiagnostic Summary:")
    if issues:
        print("Found the following issues:")
        for issue in issues:
            print(f"- {issue}")
    else:
        print("No issues found. System should be ready to run.")

    return len(issues) == 0, excel_path  # 返回檢查結果和檔案路徑

def print_label_with_printer(label_path):
    """Print the label using Gprinter GP-1134T without changing the default printer."""
    try:
        # 指定要使用的打印機名稱
        printer_name = "Gprinter GP-1134T"  # 確保這是你的打印機的正確名稱

        # 打開指定的打印機
        hprinter = win32print.OpenPrinter(printer_name)
        try:
            # 開始列印作業
            hdc = win32ui.CreateDC()
            hdc.CreatePrinterDC(printer_name)
            hdc.StartDoc(label_path)
            hdc.StartPage()

            # 打開圖片
            img = Image.open(label_path)
            dib = ImageWin.Dib(img)
            dib.draw(hdc.GetHandleOutput(), (0, 0, img.size[0], img.size[1]))

            # 結束列印作業
            hdc.EndPage()
            hdc.EndDoc()
            hdc.DeleteDC() 
        finally:
            # 關閉打印機
            win32print.ClosePrinter(hprinter)

    except Exception as e:
        print(f"Error printing label: {str(e)}")

def get_latest_excel_file(directory):
    """Get the latest Excel file from the specified directory."""
    try:
        # 使用 glob 模組查找目錄中的所有 Excel 文件
        files = glob.glob(os.path.join(directory, "*.xlsx"))
        if not files:
            raise FileNotFoundError("No Excel files found in the specified directory.")
        
        # 找到最新的文件
        latest_file = max(files, key=os.path.getmtime)
        print(f"Latest file: {latest_file}")  # 只顯示最新文件
        return latest_file
    except Exception as e:
        print(f"Error finding latest Excel file: {str(e)}")
        return None

class TicketGeneratorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("藥局標籤生成器")
        self.root.configure(bg='white')
        self.root.withdraw()  # 先隱藏主視窗
        
        # 創建進度視窗
        self.progress_window = tk.Toplevel()
        self.progress_window.title("載入中")
        self.progress_window.geometry("300x150")
        self.progress_window.configure(bg='white')
        
        # 確保進度視窗置中顯示
        screen_width = self.progress_window.winfo_screenwidth()
        screen_height = self.progress_window.winfo_screenheight()
        x = (screen_width - 300) // 2
        y = (screen_height - 150) // 2
        self.progress_window.geometry(f"300x150+{x}+{y}")
        
        # 添加載入訊息
        self.loading_label = tk.Label(
            self.progress_window,
            text="正在載入資料...",
            font=("Arial", 12),
            bg='white'
        )
        self.loading_label.pack(pady=20)
        
        # 添加進度條
        self.progress_bar = ttk.Progressbar(
            self.progress_window,
            mode='indeterminate',
            length=200
        )
        self.progress_bar.pack(pady=10)
        self.progress_bar.start(10)
        
        # 初始化變量
        self.df = None
        self.original_df = None  # 添加這行來保存原始數據
        self.search_timer = None
        self.current_excel_path = None
        self.print_enabled = tk.BooleanVar(value=False)
        
        # 加載配置文件
        self.config = self.load_config('config.txt')
        
        # 創建主框架
        self.main_frame = tk.Frame(root, bg='white')
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 創建左右框架
        self.left_frame = tk.Frame(self.main_frame, bg='white')
        self.right_frame = tk.Frame(self.main_frame, bg='white')
        self.left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 創建界面元件
        self.create_print_control()
        self.create_search_section()
        self.create_preview_section()
        self.create_non_ethicals_controls()  # 添加這行
        self.create_excel_preview()
        
        print("初始化完成：所有控制項已創建")
        
        # 使用 after 方法來載入 Excel 文件
        self.root.after(100, self.initialize_application)

    def load_config(self, file_path):
        """從 config.txt 加載配置"""
        config = configparser.ConfigParser()
        config.read(file_path)
        return config

    def initialize_application(self):
        """初始化應用程式並載入 Excel"""
        try:
            # 更新載入訊息
            self.loading_label.config(text="正在載入 Excel 文件...")
            
            # 載入 Excel 文件
            success = self.load_latest_excel()
            
            if success:
                # 確保 Excel 預覽已更新
                self.root.update_idletasks()
                
                # 更新載入訊息
                self.loading_label.config(text="載入完成！")
                
                # 停止進度條動畫
                self.progress_bar.stop()
                
                # 延遲關閉進度視窗並顯示主視窗
                self.root.after(1000, self.finish_loading)
            else:  # 這個 else 應該和 if 對齊
                self.loading_label.config(text="載入失敗！", fg="red")
                self.root.after(2000, self.finish_loading)
                
        except Exception as e:
            # 發生錯誤時顯示錯誤訊息
            self.loading_label.config(text=f"發生錯誤：{str(e)}", fg="red")
            self.root.after(2000, self.finish_loading)

    def finish_loading(self):
        """完成載入程序"""
        # 關閉進度視窗
        self.progress_window.destroy()
        
        # 顯示主視窗
        self.root.deiconify()
        
        # 設置主視窗位置（置中）
        self.center_window()
        
        # 聚焦到搜尋框
        self.search_entry.focus()

    def center_window(self):
        """將主視窗置中"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.root.geometry(f"+{x}+{y}")

    def create_print_control(self):
        """創建列印控制區域"""
        control_frame = tk.Frame(self.left_frame, bg='white')
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 左側：列印開關
        left_frame = tk.Frame(control_frame, bg='white')
        left_frame.pack(side=tk.LEFT, padx=5)
        
        print_check = ttk.Checkbutton(
            left_frame,
            text="自動列印標籤",
            variable=self.print_enabled,
            style='Switch.TCheckbutton'
        )
        print_check.pack(side=tk.LEFT)
        
        # 右側：調整控制區
        right_frame = tk.Frame(control_frame, bg='white')
        right_frame.pack(side=tk.RIGHT, padx=5)
        
        # 創建所有Spinbox但先隱藏
        self.create_adjustment_spinboxes(right_frame)

    def create_adjustment_spinboxes(self, parent):
        """創建調整用的Spinbox"""
        # 條碼位置調整
        self.barcode_label = tk.Label(parent, text="條碼位置 (Y):", bg='white')
        self.barcode_spinbox = ttk.Spinbox(
            parent,
            from_=-100,
            to=200,
            increment=5,
            width=10
        )
        
        # 文字X軸位置調整
        self.text_x_label = tk.Label(parent, text="文字位置 (X):", bg='white')
        self.text_x_spinbox = ttk.Spinbox(
            parent,
            from_=0,
            to=300,
            increment=5,
            width=10
        )
        
        # 文字Y軸位置調整
        self.text_y_label = tk.Label(parent, text="文字位置 (Y):", bg='white')
        self.text_y_spinbox = ttk.Spinbox(
            parent,
            from_=0,
            to=300,
            increment=5,
            width=10
        )
        
        # 文字大小調整
        self.font_size_label = tk.Label(parent, text="文字大小:", bg='white')
        self.font_size_spinbox = ttk.Spinbox(
            parent,
            from_=10,
            to=100,
            increment=1,
            width=10
        )
        
        # 初始隱藏所有控制項
        self.hide_adjustment_controls()
        
        # 綁定事件
        for spinbox in [self.barcode_spinbox, self.text_x_spinbox, 
                       self.text_y_spinbox, self.font_size_spinbox]:
            spinbox.bind('<Return>', self.update_settings)
            spinbox.bind('<<Increment>>', self.update_settings)
            spinbox.bind('<<Decrement>>', self.update_settings)
            spinbox.bind('<FocusOut>', self.update_settings)

    def show_adjustment_controls(self):
        """顯示調整控制項"""
        # 條碼位置
        self.barcode_label.pack(side=tk.TOP, pady=2)
        self.barcode_spinbox.pack(side=tk.TOP, pady=2)
        
        # 文字X軸位置
        self.text_x_label.pack(side=tk.TOP, pady=2)
        self.text_x_spinbox.pack(side=tk.TOP, pady=2)
        
        # 文字Y軸位置
        self.text_y_label.pack(side=tk.TOP, pady=2)
        self.text_y_spinbox.pack(side=tk.TOP, pady=2)
        
        # 文字大小
        self.font_size_label.pack(side=tk.TOP, pady=2)
        self.font_size_spinbox.pack(side=tk.TOP, pady=2)

    def hide_adjustment_controls(self):
        """隱藏調整控制項"""
        for widget in [self.barcode_label, self.barcode_spinbox,
                      self.text_x_label, self.text_x_spinbox,
                      self.text_y_label, self.text_y_spinbox,
                      self.font_size_label, self.font_size_spinbox]:
            widget.pack_forget()

    def update_settings(self, event=None):
        """更新設定值"""
        try:
            # 保存當前搜尋文字
            current_search = self.search_entry.get().strip()
            hidden_search = self.hidden_search_entry.get().strip()
            
            # 更新配置文件
            self.config.set('barcode_options_ethicals', 'position_offset_y', 
                           self.barcode_spinbox.get())
            self.config.set('text_offsets_ethicals', 'item_description_offset_x', 
                           self.text_x_spinbox.get())
            self.config.set('text_offsets_ethicals', 'item_description_offset_y', 
                           self.text_y_spinbox.get())
            self.config.set('font_sizes_ethicals', 'item_description', 
                           self.font_size_spinbox.get())
            
            with open('config.txt', 'w', encoding='utf-8') as configfile:
                self.config.write(configfile)
            
            # 如果有搜尋文字，重新生成標籤
            search_text = current_search if current_search else hidden_search
            if search_text:
                self.search_and_print(keep_search_text=True)
                
        except Exception as e:
            messagebox.showerror("錯誤", f"更新設定時發生錯誤：{str(e)}")

    def create_preview_section(self):
        """創建預覽區域"""
        # 預覽框架
        preview_frame = tk.LabelFrame(
            self.left_frame, 
            text=" 預覽 ", 
            font=("Arial", 12, "bold"),
            bg='white',
            fg='#2c3e50'
        )
        preview_frame.pack(fill=tk.X, padx=5, pady=(5, 0))
        
        # 預覽標籤
        self.preview_label = tk.Label(
            preview_frame,
            text="等待搜尋...",
            bg='white',
            font=("Arial", 12)
        )
        self.preview_label.pack(fill=tk.BOTH, padx=5, pady=5)

        # 創建初始調整框架
        self.initial_frame = tk.LabelFrame(
            self.left_frame,
            text=" 調整區域 ",
            font=("Arial", 12, "bold"),
            bg='white',
            fg='#2c3e50'
        )
        self.initial_frame.pack(fill=tk.X, padx=5, pady=(5, 0))
        self.initial_frame.pack_propagate(False)
        self.initial_frame.configure(height=200)

        # 修改額外框架的設置
        self.extra_frame = tk.LabelFrame(
            self.left_frame,
            text=" 列印統計 ",
            font=("Arial", 12, "bold"),
            bg='white',
            fg='#2c3e50'
        )
        self.extra_frame.pack_propagate(False)
        self.extra_frame.configure(height=200)

        # 創建統計信息容器
        stats_container = tk.Frame(self.extra_frame, bg='white')
        stats_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 成功生成標籤計數
        self.success_label = tk.Label(
            stats_container,
            text="成功生成標籤: 0",
            font=("Arial", 11),
            bg='white',
            fg='#27ae60'  # 綠色
        )
        self.success_label.pack(anchor='w', pady=2)

        # 失敗生成標籤計數
        self.failed_label = tk.Label(
            stats_container,
            text="無法生成標籤: 0",
            font=("Arial", 11),
            bg='white',
            fg='#c0392b'  # 紅色
        )
        self.failed_label.pack(anchor='w', pady=2)

        # 分隔線
        separator = tk.Frame(stats_container, height=2, bg='#bdc3c7')
        separator.pack(fill=tk.X, pady=5)

        # 當前時間顯示
        self.time_label = tk.Label(
            stats_container,
            text="當前時間: --:--:--",
            font=("Arial", 11),
            bg='white'
        )
        self.time_label.pack(anchor='w', pady=2)

        # 開始更新時間
        self.update_time()

        # 創建 Ethicals 和 non-Ethicals 調整區域
        self.create_ethicals_controls()
        self.create_non_ethicals_controls()

    def update_time(self):
        """更新當前時間顯示"""
        current_time = time.strftime("%H:%M:%S")
        if hasattr(self, 'time_label'):
            self.time_label.config(text=f"當前時間: {current_time}")
        # 每秒更新一次
        self.left_frame.after(1000, self.update_time)

    def update_stats(self, success_count, failed_count):
        """更新統計信息"""
        if hasattr(self, 'success_label'):
            self.success_label.config(text=f"成功生成標籤: {success_count}")
        if hasattr(self, 'failed_label'):
            self.failed_label.config(text=f"無法生成標籤: {failed_count}")

    def create_ethicals_controls(self):
        """創建 Ethicals 調整控制項"""
        try:
            # Ethicals 控制框架
            self.ethicals_frame = tk.LabelFrame(
                self.left_frame,
                text=" Ethicals 調整 ",
                font=("Arial", 12, "bold"),
                bg='white',
                fg='#2c3e50'
            )
            
            # 設置固定大小
            self.ethicals_frame.pack(fill=tk.X, padx=5, pady=(5, 0))
            self.ethicals_frame.pack_propagate(False)  # 禁止自動調整大小
            self.ethicals_frame.configure(height=200)  # 設置為 200 像素高
            
            # 條碼位置調整
            barcode_frame = tk.Frame(self.ethicals_frame, bg='white')
            barcode_frame.pack(fill=tk.X, padx=5, pady=2)
            print("創建條碼位置框架成功")
            
            tk.Label(
                barcode_frame,
                text="條碼位置 (Y):",
                font=("Arial", 10),
                bg='white'
            ).pack(side=tk.LEFT)
            
            self.position_spinbox = ttk.Spinbox(
                barcode_frame,
                from_=-100,
                to=200,
                increment=5,
                width=8
            )
            self.position_spinbox.pack(side=tk.LEFT, padx=2)

            # 文字X位置調整
            text_x_frame = tk.Frame(self.ethicals_frame, bg='white')
            text_x_frame.pack(fill=tk.X, padx=5, pady=2)
            
            tk.Label(
                text_x_frame,
                text="文字位置 (X):",
                font=("Arial", 10),
                bg='white'
            ).pack(side=tk.LEFT)
            
            self.text_x_spinbox = ttk.Spinbox(
                text_x_frame,
                from_=0,
                to=100,
                increment=5,
                width=8
            )
            self.text_x_spinbox.pack(side=tk.LEFT, padx=2)

            # 文字Y位置調整
            text_y_frame = tk.Frame(self.ethicals_frame, bg='white')
            text_y_frame.pack(fill=tk.X, padx=5, pady=2)
            
            tk.Label(
                text_y_frame,
                text="文字位置 (Y):",
                font=("Arial", 10),
                bg='white'
            ).pack(side=tk.LEFT)
            
            self.text_y_spinbox = ttk.Spinbox(
                text_y_frame,
                from_=0,
                to=100,
                increment=5,
                width=8
            )
            self.text_y_spinbox.pack(side=tk.LEFT, padx=2)

            # 文字大小調整
            font_frame = tk.Frame(self.ethicals_frame, bg='white')
            font_frame.pack(fill=tk.X, padx=5, pady=2)
            
            tk.Label(
                font_frame,
                text="文字大小:",
                font=("Arial", 10),
                bg='white'
            ).pack(side=tk.LEFT)
            
            self.font_size_spinbox = ttk.Spinbox(
                font_frame,
                from_=10,
                to=100,
                increment=1,
                width=8
            )
            self.font_size_spinbox.pack(side=tk.LEFT, padx=2)

            # 綁定事件
            print("綁定 Spinbox 事件")
            for spinbox in [self.position_spinbox, self.text_x_spinbox, 
                           self.text_y_spinbox, self.font_size_spinbox]:
                spinbox.bind('<Return>', self.update_ethicals_settings)
                spinbox.bind('<<Increment>>', self.update_ethicals_settings)
                spinbox.bind('<<Decrement>>', self.update_ethicals_settings)
                spinbox.bind('<FocusOut>', self.update_ethicals_settings)

            print("Ethicals 控制項創建完成")
            # 初始時隱藏
            self.ethicals_frame.pack_forget()
            print("控制項已初始隱藏")

        except Exception as e:
            print(f"創建 Ethicals 控制項時發生錯誤: {str(e)}")
            traceback.print_exc()

    def update_ethicals_settings(self, event=None):
        """更新 Ethicals 設定"""
        print("\n=== 更新 Ethicals 設定 ===")
        try:
            # 保存當前搜尋文字
            current_search = self.search_entry.get().strip()
            hidden_search = self.hidden_search_entry.get().strip()
            
            print(f"當前值:")
            print(f"- 條碼位置 Y: {self.position_spinbox.get()}")
            print(f"- 文字位置 X: {self.text_x_spinbox.get()}")
            print(f"- 文字位置 Y: {self.text_y_spinbox.get()}")
            print(f"- 文字大小: {self.font_size_spinbox.get()}")
            
            # 更新配置文件
            self.config.set('barcode_options_ethicals', 'position_offset_y', 
                           str(self.position_spinbox.get()))
            self.config.set('text_offsets_ethicals', 'item_description_offset_x', 
                           str(self.text_x_spinbox.get()))
            self.config.set('text_offsets_ethicals', 'item_description_offset_y', 
                           str(self.text_y_spinbox.get()))
            self.config.set('font_sizes_ethicals', 'item_description', 
                           str(self.font_size_spinbox.get()))
            
            with open('config.txt', 'w', encoding='utf-8') as configfile:
                self.config.write(configfile)
            print("配置文件已更新")
            
            # 使用隱藏搜尋框的值重新生成標籤
            search_text = hidden_search if hidden_search else current_search
            if search_text:
                print(f"使用搜尋文字重新生成標籤: {search_text}")
                # 暫時設置搜尋文字
                self.search_entry.delete(0, tk.END)
                self.search_entry.insert(0, search_text)
                # 生成標籤
                self.search_and_print(keep_search_text=True)
                
        except Exception as e:
            print(f"更新設定時發生錯誤: {str(e)}")
            traceback.print_exc()

    def create_search_section(self):
        """創建搜尋區域"""
        search_frame = tk.Frame(self.left_frame, bg='white')
        search_frame.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Label(search_frame, 
                text="請輸入商品代碼：",
                font=("Arial", 10),
                bg='white').pack(side=tk.LEFT)
        
        # 主搜尋框
        self.search_entry = ttk.Entry(search_frame, width=30)
        self.search_entry.pack(side=tk.LEFT, padx=5)
        self.search_entry.bind('<KeyRelease>', self.on_search_key_release)
        
        # 創建隱藏的備份輸入框
        self.hidden_search_entry = ttk.Entry(search_frame)
        self.hidden_search_entry.pack_forget()  # 不顯示這個輸入框
        
        # 確保搜尋框永遠可用
        self.search_entry.config(state='normal')
        self.search_entry.focus()

    def create_result_section(self):
        """創建結果顯示區域"""
        result_frame = tk.Frame(self.left_frame, bg='white')
        result_frame.pack(fill=tk.X, pady=10)
        
        self.result_label = tk.Label(
            result_frame,
            text="",
            font=("Arial", 11),
            bg='white',
            justify=tk.LEFT,
            anchor='w'
        )
        self.result_label.pack(fill=tk.X, padx=5, pady=5)

    def on_search_key_release(self, event):
        """處理按鍵釋放事件"""
        # 取消之前的計時器
        if self.search_timer:
            self.root.after_cancel(self.search_timer)
        # 設置新的 0.5 秒延遲計時器
        self.search_timer = self.root.after(500, self.search_and_print)

    def search_and_print(self, event=None, keep_search_text=False):
        try:
            search_text = self.search_entry.get().strip()
            print(f"\n=== 搜尋過程開始 ===")
            print(f"搜尋文字: '{search_text}'")
            
            if not search_text:
                print("搜尋文字為空，返回")
                return

            # 在 DataFrame 中搜尋
            match = self.df[
                self.df['Item Code'].astype(str).str.contains(search_text, case=False) |
                self.df['Item Description'].astype(str).str.contains(search_text, case=False)
            ]
            
            if not match.empty:
                item_data = match.iloc[0]
                department = str(item_data['Department'])
                print(f"找到匹配項目：{item_data['Item Description']}")
                print(f"部門：{department}")
                
                # 隱藏初始框架
                self.initial_frame.pack_forget()
                
                # 檢查部門並顯示/隱藏控制項
                if department.strip().lower() == "ethicals":
                    print("處理 Ethicals 部門")
                    self.ethicals_frame.pack(fill=tk.X, padx=5, pady=(5, 0))
                    if hasattr(self, 'non_ethicals_frame'):
                        self.non_ethicals_frame.pack_forget()
                    # 顯示額外框架
                    self.extra_frame.pack(fill=tk.X, padx=5, pady=(5, 0))
                else:
                    print("處理非 Ethicals 部門")
                    self.ethicals_frame.pack_forget()
                    if hasattr(self, 'non_ethicals_frame'):
                        self.non_ethicals_frame.pack(fill=tk.X, padx=5, pady=(5, 0))
                    # 顯示額外框架
                    self.extra_frame.pack(fill=tk.X, padx=5, pady=(5, 0))
                
                # 如果搜尋成功，保存到隱藏搜尋框
                if not keep_search_text:
                    self.hidden_search_entry.delete(0, tk.END)
                    self.hidden_search_entry.insert(0, search_text)
                    print(f"保存搜尋文字到隱藏框: {search_text}")
                
                # 檢查部門並顯示/隱藏控制項
                if department.strip().lower() == "ethicals":
                    print("處理 Ethicals 部門")
                    self.ethicals_frame.pack(fill=tk.X, padx=5, pady=(5, 0))
                    if hasattr(self, 'non_ethicals_frame'):
                        self.non_ethicals_frame.pack_forget()
                    try:
                        print("設置 Ethicals Spinbox 值")
                        self.position_spinbox.set(self.config.get('barcode_options_ethicals', 'position_offset_y', fallback=65))
                        self.text_x_spinbox.set(self.config.get('text_offsets_ethicals', 'item_description_offset_x', fallback=10))
                        self.text_y_spinbox.set(self.config.get('text_offsets_ethicals', 'item_description_offset_y', fallback=30))
                        self.font_size_spinbox.set(self.config.get('font_sizes_ethicals', 'item_description', fallback=50))
                    except Exception as e:
                        print(f"設置 Ethicals Spinbox 值時發生錯誤: {str(e)}")
                else:
                    print("處理非 Ethicals 部門")
                    self.ethicals_frame.pack_forget()
                    if hasattr(self, 'non_ethicals_frame'):
                        print("顯示非 Ethicals 控制項")
                        self.non_ethicals_frame.pack(fill=tk.X, padx=5, pady=(5, 0))
                        try:
                            print("設置非 Ethicals Spinbox 值")
                            self.non_ethicals_position_spinbox.set(
                                self.config.get('barcode_options', 'position_offset_y', fallback=-140))
                            self.non_ethicals_desc_y_spinbox.set(
                                self.config.get('text_offsets', 'item_description_offset_y', fallback=200))
                            self.non_ethicals_retail_y_spinbox.set(
                                self.config.get('text_offsets', 'retail_offset_y', fallback=80))
                            # 添加字體大小設置
                            self.non_ethicals_desc_font_spinbox.set(
                                self.config.get('font_sizes', 'item_description', fallback=30))
                            self.non_ethicals_retail_font_spinbox.set(
                                self.config.get('font_sizes', 'retail', fallback=100))
                            
                            # 設置 RRP 和日期位置的預設值
                            self.non_ethicals_rrp_x_spinbox.set(
                                self.config.get('text_offsets', 'rrp_offset_x', fallback=-20))
                            self.non_ethicals_rrp_y_spinbox.set(
                                self.config.get('text_offsets', 'rrp_offset_y', fallback=-280))
                            self.non_ethicals_date_x_spinbox.set(
                                self.config.get('text_offsets', 'date_offset_x', fallback=20))
                            self.non_ethicals_date_y_spinbox.set(
                                self.config.get('text_offsets', 'date_offset_y', fallback=-65))
                            
                        except Exception as e:
                            print(f"設置非 Ethicals Spinbox 值時發生錯誤: {str(e)}")
                    else:
                        print("警告：non_ethicals_frame 未創建")
                
                print("開始生成標籤圖像")
                # 生成標籤圖像
                label_image = self.create_label_image(item_data)
                
                print("更新預覽")
                # 更新預覽
                self.update_preview(label_image)
                
                # 如果不是保持搜尋文字，則清空主搜尋框
                if not keep_search_text:
                    self.search_entry.delete(0, tk.END)
                
                # 如果啟用列印且生成成功
                if self.print_enabled.get() and label_image:
                    print("執行列印")
                    self.print_label(label_image)
            else:
                # 如果沒有找到匹配項，顯示初始框架
                self.initial_frame.pack(fill=tk.X, padx=5, pady=(5, 0))
                self.ethicals_frame.pack_forget()
                self.non_ethicals_frame.pack_forget()
                self.extra_frame.pack_forget()
                
        except Exception as e:
            print(f"搜尋過程發生錯誤: {str(e)}")
            traceback.print_exc()
            self.update_preview(None)

    def create_barcode(self, item_code, options):
        """生成條碼"""
        barcode = Code128(str(item_code), writer=ImageWriter())
        
        # 設置條碼選項
        opts = {
            'module_width': float(options['module_width']),
            'module_height': float(options['module_height']),
            'quiet_zone': float(options['quiet_zone']),
            'write_text': options.getboolean('write_text', False)
        }
        
        # 生成條碼圖像
        barcode_image = barcode.render(opts)
        
        # 調整大小
        width = int(barcode_image.size[0] * float(options['resize_width_factor']))
        height = int(barcode_image.size[1] * float(options['resize_height_factor']))
        barcode_image = barcode_image.resize((width, height), Image.LANCZOS)
        
        return barcode_image

    def create_label_image(self, row):
        """創建標籤圖像"""
        print("\n=== 開始生成標籤圖像 ===")
        try:
            # 獲取必要的數據
            item_desc = str(row['Item Description'])
            item_code = str(row['Item Code'])
            sale_price = float(row.get('Sale Price', 0))
            retail = float(row.get('Retail', 0))
            rrp = float(row.get('RRP', 0))
            modified_date = pd.to_datetime(row['Modified']).strftime('%Y-%m-%d')
            department = str(row['Department'])
            
            print(f"商品資訊:")
            print(f"- 描述: {item_desc}")
            print(f"- 代碼: {item_code}")
            print(f"- 部門: {department}")
            print(f"- 零售價: {retail}")
            print(f"- RRP: {rrp}")
            
            # 使用 create_image 函數生成標籤
            create_image(
                item_code=item_code,
                item_description=item_desc,
                retail=retail,
                rrp=rrp,
                modified_date=modified_date,
                excel_file_path=self.current_excel_path,
                department=department
            )
            
            # 讀取生成的圖像
            image_path = 'output_image_with_barcode.png'
            if os.path.exists(image_path):
                print(f"標籤圖像生成成功: {image_path}")
                return Image.open(image_path)
            
            print("標籤圖像生成失敗")
            return None
            
        except Exception as e:
            print(f"生成標籤圖像時發生錯誤: {str(e)}")
            traceback.print_exc()
            return None

    def print_label(self, image):
        """列印標籤"""
        printer_name = self.config.get('Settings', 'printer_name')
        
        # 獲取印表機 DC
        hprinter = win32print.OpenPrinter(printer_name)
        printer_info = win32print.GetPrinter(hprinter, 2)
        pdc = win32ui.CreateDC()
        pdc.CreatePrinterDC(printer_name)
        
        try:
            # 開始文檔
            pdc.StartDoc('Label')
            pdc.StartPage()
            
            # 將圖像轉換為設備相關的點陣圖
            dib = ImageWin.Dib(image)
            dib.draw(pdc.GetHandleOutput(), (0, 0, image.size[0], image.size[1]))
            
            # 結束列印
            pdc.EndPage()
            pdc.EndDoc()
            
        finally:
            # 清理資源
            pdc.DeleteDC()
            win32print.ClosePrinter(hprinter)

    def generate_label(self, row):
        """生成標籤"""
        try:
            # 獲取輸出路徑
            output_folder = self.config.get('Settings', 'output_folder')
            
            # 確保輸出目錄存在
            if not os.path.exists(output_folder):
                os.makedirs(output_folder)
            
            # 固定的輸出文件名
            filename = "Output_label.png"
            output_path = os.path.join(output_folder, filename)
            
            # 如果文件已存在，先嘗試刪除
            if os.path.exists(output_path):
                try:
                    os.remove(output_path)
                except PermissionError:
                    messagebox.showerror("錯誤", "無法覆蓋現有文件，請確保文件未被其他程序使用")
                    return  # 修正縮排
                except Exception as e:
                    messagebox.showerror("錯誤", f"刪除現有文件時發生錯誤：{str(e)}")
                    return
            
            # 創建標籤圖像
            label_image = self.create_label_image(row)
            
            # 保存新的標籤圖像
            label_image.save(output_path)
            
            # 根據開關狀態決定是否列印
            if self.print_enabled.get():
                self.print_label(output_path)
                status_text = f"已生成並列印標籤：{output_path}"
            else:  # 這個 else 是正確的
                status_text = f"已生成標籤：{output_path}"
            
            # 更新界面顯示
            self.result_label.config(text=status_text)
            
            # 清空輸入框並重新聚焦
            self.search_entry.delete(0, tk.END)
            self.search_entry.focus()
            
        except Exception as e:
            messagebox.showerror("錯誤", f"生成標籤時發生錯誤：{str(e)}")

    def select_excel_file(self):
        """手動選擇 Excel 文件"""
        try:
            file_path = filedialog.askopenfilename(
                title="選擇 Excel 文件",
                initialdir=self.excel_directory,
                filetypes=[("Excel files", "*.xlsx *.xls")]
            )
            
            if file_path:
                self.df = pd.read_excel(file_path)
                messagebox.showinfo("成功", f"已載入文件：{os.path.basename(file_path)}")
                # 更新當前目錄
                self.excel_directory = os.path.dirname(file_path)
            else:
                messagebox.showinfo("提示", "未選擇文件")
                
        except Exception as e:
            messagebox.showerror("錯誤", f"載入文件時發生錯誤：{str(e)}")

    def update_preview(self, image):
        """更新預覽圖像"""
        try:
            if image:
                # 調整圖像大小以適應預覽
                preview_width = 200
                preview_height = 120
                image = image.resize((preview_width, preview_height), Image.Resampling.LANCZOS)
                
                # 轉換為 PhotoImage
                photo = ImageTk.PhotoImage(image)
                
                # 更新預覽標籤
                self.preview_label.config(image=photo, text='')
                self.preview_label.image = photo  # 保持引用
                print("預覽圖像已更新")
            else:
                # 如果沒有圖像，顯示提示文字
                self.preview_label.config(image='', text="等待搜尋...")
                print("預覽已重置為等待狀態")
                
        except Exception as e:
            print(f"更新預覽時發生錯誤: {str(e)}")
            self.preview_label.config(image='', text="預覽生成失敗")

    def create_excel_preview(self):
        """創建 Excel 預覽區域"""
        preview_frame = tk.LabelFrame(self.right_frame,
                                    text=" Excel 數據預覽 ",
                                    font=("Arial", 12, "bold"),
                                    bg='white',
                                    fg='#2c3e50')
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 文件路徑框架
        path_frame = tk.Frame(preview_frame, bg='white')
        path_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 文件路徑標籤
        tk.Label(path_frame,
                text="當前文件：",
                font=("Arial", 10),
                bg='white').pack(side=tk.LEFT)
        
        # 文件路徑顯示（使用 Entry 而不是 Label，以便可以滾動查看完整路徑）
        self.file_path_entry = ttk.Entry(path_frame, state='readonly')
        self.file_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 按鈕框架
        button_frame = tk.Frame(preview_frame, bg='white')
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 刷新按鈕
        refresh_btn = ttk.Button(button_frame,
                                text="刷新",
                                command=self.refresh_excel)
        refresh_btn.pack(side=tk.LEFT, padx=5)
        
        # 選擇文件按鈕
        choose_btn = ttk.Button(button_frame,
                               text="選擇文件",
                               command=self.choose_excel_file)
        choose_btn.pack(side=tk.LEFT, padx=5)
        
        # 在搜尋框架之前添加日期選擇框架
        date_frame = tk.Frame(preview_frame, bg='white')
        date_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 日期範圍標籤
        date_label = tk.Label(
            date_frame,
            text="日期範圍：",
            font=("Arial", 10),
            bg='white'
        )
        date_label.pack(side=tk.LEFT)
        
        # 開始日期選擇器
        start_label = tk.Label(
            date_frame,
            text="從",
            font=("Arial", 10),
            bg='white'
        )
        start_label.pack(side=tk.LEFT, padx=(5,2))
        
        self.start_date = DateEntry(
            date_frame,
            width=12,
            background='darkblue',
            foreground='white',
            borderwidth=2,
            date_pattern='yyyy-mm-dd'
        )
        self.start_date.pack(side=tk.LEFT, padx=2)
        
        # 結束日期選擇器
        end_label = tk.Label(
            date_frame,
            text="到",
            font=("Arial", 10),
            bg='white'
        )
        end_label.pack(side=tk.LEFT, padx=(5,2))
        
        self.end_date = DateEntry(
            date_frame,
            width=12,
            background='darkblue',
            foreground='white',
            borderwidth=2,
            date_pattern='yyyy-mm-dd'
        )
        self.end_date.pack(side=tk.LEFT, padx=2)
        
        # 清除日期按鈕
        clear_btn = ttk.Button(
            date_frame,
            text="清除日期過濾",
            command=self.clear_date_filter
        )
        clear_btn.pack(side=tk.LEFT, padx=5)
        
        # 綁定日期選擇事件
        self.start_date.bind("<<DateEntrySelected>>", self.apply_date_filter)
        self.end_date.bind("<<DateEntrySelected>>", self.apply_date_filter)
        
        # 搜尋框架
        search_frame = tk.Frame(preview_frame, bg='white')
        search_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 搜尋標籤
        tk.Label(search_frame,
                text="搜尋：",
                font=("Arial", 10),
                bg='white').pack(side=tk.LEFT, padx=(0,5))
        
        # Excel 預覽搜尋框
        self.excel_search_entry = ttk.Entry(search_frame)
        self.excel_search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 添加搜尋按鈕
        self.search_button = ttk.Button(
            search_frame,
            text="搜尋",
            command=self.perform_excel_search
        )
        self.search_button.pack(side=tk.LEFT, padx=5)
        
        # 綁定 Enter 鍵到搜尋功能
        self.excel_search_entry.bind('<Return>', lambda e: self.perform_excel_search())
        
        # 移除原有的即時搜尋綁定
        # self.excel_search_entry.bind('<KeyRelease>', self.search_excel_preview)
        
        # 創建 Treeview 的容器框架
        tree_frame = tk.Frame(preview_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        # 創建 Treeview
        self.tree = ttk.Treeview(tree_frame,
                                columns=("Modified", "Item Description", "Item Code", 
                                       "Sale Price", "Retail", "Alias"),
                                show='headings',
                                height=20)
        
        # 設置列標題和排序功能
        columns_config = {
            "Modified": {"text": "Last Update", "width": 100},
            "Item Description": {"text": "Name", "width": 200},
            "Item Code": {"text": "Item Code", "width": 100},
            "Sale Price": {"text": "Sale Price", "width": 80},
            "Retail": {"text": "Retail", "width": 80},
            "Alias": {"text": "Alias", "width": 100}
        }
        
        # 添加排序狀態追踪
        self.sort_state = {"column": "Modified", "reverse": False}
        
        # 設置每個列的標題和排序功能
        for col, config in columns_config.items():
            self.tree.heading(col, 
                             text=config["text"],
                             command=lambda c=col: self.sort_treeview(c))
            self.tree.column(col, width=config["width"])
        
        # 創建滾動條
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 放置 Treeview 和滾動條
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 綁定選擇事件
        self.tree.bind('<<TreeviewSelect>>', self.on_tree_select)

    def perform_excel_search(self):
        """執行 Excel 搜尋"""
        try:
            print("\n=== 執行搜尋 ===")
            search_text = self.excel_search_entry.get().lower().strip()
            
            # 先獲取日期過濾後的數據範圍
            start_date = pd.to_datetime(self.start_date.get_date())
            end_date = pd.to_datetime(self.end_date.get_date())
            end_date = end_date + pd.Timedelta(days=1) - pd.Timedelta(microseconds=1)
            
            # 在原始數據中應用日期過濾
            date_mask = (pd.to_datetime(self.original_df['Modified']) >= start_date) & \
                       (pd.to_datetime(self.original_df['Modified']) <= end_date)
            date_filtered_df = self.original_df[date_mask]
            
            # 如果搜尋框是空的，只顯示日期過濾的結果
            if not search_text:
                self.update_treeview_with_data(date_filtered_df)
                print(f"顯示日期範圍內的所有數據: {len(date_filtered_df)} 筆")
                return
            
            # 在日期過濾後的數據中進行搜尋
            search_mask = date_filtered_df.apply(lambda row: any(
                str(value).lower().find(search_text) != -1 
                for value in row.values if pd.notnull(value)
            ), axis=1)
            
            final_filtered_df = date_filtered_df[search_mask]
            
            # 更新 Treeview 顯示
            self.update_treeview_with_data(final_filtered_df)
            
            print(f"在日期範圍內找到 {len(final_filtered_df)} 個匹配項")
            
        except Exception as e:
            print(f"搜尋時發生錯誤: {str(e)}")
            traceback.print_exc()

    def on_tree_select(self, event=None):
        """處理 Treeview 選擇事件"""
        try:
            selected_items = self.tree.selection()
            if selected_items:
                # 獲取選中行的值
                item_values = self.tree.item(selected_items[0])['values']
                item_code = str(item_values[2]).strip()
                
                # 檢查原始數據中的格式
                if self.df is not None:
                    mask = self.df['Item Code'].astype(str).apply(lambda x: x.strip() == item_code)
                    original_code = self.df[mask]['Item Code']
                    if not original_code.empty:
                        item_code = str(original_code.iloc[0]).zfill(len(item_code))
                
                # 設置到標籤搜尋框
                self.search_entry.delete(0, tk.END)
                self.search_entry.insert(0, item_code)
                self.search_entry.config(state='normal')
                
                # 執行搜尋
                self.search_and_print()
                
                # 清空搜尋框並設置焦點
                self.root.after(100, self.clear_and_focus_search_entry)
                
        except Exception as e:
            print(f"Treeview 選擇事件發生錯誤: {str(e)}")

    def update_excel_preview(self):
        """更新 Excel 預覽數據"""
        try:
            if self.df is None:
                print("DataFrame is None")
                return
            if not hasattr(self, 'tree'):
                print("Treeview not created")
                return
            
            print(f"Updating Excel preview with {len(self.df)} rows")
            
            # 清空現有數據
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # 確保所需的列存在
            required_columns = ['Modified', 'Item Description', 'Item Code', 'Sale Price', 'Retail', 'Alias']
            missing_columns = [col for col in required_columns if col not in self.df.columns]
            if missing_columns:
                print(f"Missing columns: {missing_columns}")
                return
            
            # 按照 Modified 列排序數據（最新的在前）
            sorted_df = self.df.sort_values('Modified', ascending=False)
            
            # 添加數據到 Treeview
            for _, row in sorted_df.iterrows():
                try:
                    modified_date = pd.to_datetime(row['Modified']).strftime('%Y-%m-%d %H:%M')
                    sale_price = f"${float(row['Sale Price']):.2f}" if pd.notnull(row['Sale Price']) else ''
                    retail = f"${float(row['Retail']):.2f}" if pd.notnull(row['Retail']) else ''
                    
                    values = (
                        modified_date,
                        str(row['Item Description']),
                        str(row['Item Code']),
                        sale_price,
                        retail,
                        str(row.get('Alias', ''))
                    )
                    
                    self.tree.insert('', 'end', values=values)
                except Exception as e:
                    print(f"Error processing row: {str(e)}")
                    continue
            
            # 確保更新完成
            self.root.update_idletasks()
            print(f"Excel preview updated with {len(self.tree.get_children())} rows")
            
        except Exception as e:
            print(f"Error in update_excel_preview: {str(e)}")
            import traceback
            print(traceback.format_exc())

    def enable_search_entries(self):
        """確保搜尋框可以輸入"""
        self.search_entry.config(state='normal')
        self.excel_search_entry.config(state='normal')
        self.search_entry.focus()

    def update_results(self, item_data):
        """更新搜尋結果和預覽"""
        try:
            # 格式化顯示文本
            display_text = (
                f"商品代碼: {item_data['Item Code']}\n"
                f"商品名稱: {item_data['Item Description']}\n"
                f"部門: {item_data['Department']}\n"
                f"售價: ${item_data['Sale Price']:.2f}\n"
                f"零售價: ${item_data['Retail']:.2f}"
            )
            
            # 更新結果標籤
            self.result_label.config(text=display_text)
            
            # 檢查部門並顯示/隱藏控制項
            department = str(item_data['Department'])
            if department.strip().lower() == "ethicals":
                # 顯示控制區域
                self.ethicals_frame.pack(fill=tk.X, padx=5, pady=(5, 0))
                
                # 設置當前值
                try:
                    # 從配置文件讀取當前值
                    self.position_spinbox.set(self.config.get('barcode_options_ethicals', 'position_offset_y', fallback=65))
                    self.text_x_spinbox.set(self.config.get('text_offsets_ethicals', 'item_description_offset_x', fallback=10))
                    self.text_y_spinbox.set(self.config.get('text_offsets_ethicals', 'item_description_offset_y', fallback=30))
                    self.font_size_spinbox.set(self.config.get('font_sizes_ethicals', 'item_description', fallback=50))
                except Exception as e:
                    print(f"設置 Spinbox 值時發生錯誤: {str(e)}")
            else:  # 這個 else 的縮排有問題
                # 隱藏控制區域
                self.ethicals_frame.pack_forget()  # 修正縮排

            # 生成標籤
            modified_date = pd.to_datetime(item_data['Modified']).strftime('%Y-%m-%d')
            
            # 創建標籤圖像
            create_image(
                str(item_data['Item Code']),
                str(item_data['Item Description']),
                float(item_data['Retail']),
                float(item_data.get('RRP', 0)),
                modified_date,
                self.current_excel_path,
                department
            )
            
            # 更新預覽
            self.update_preview('output_image_with_barcode.png')
            
        except Exception as e:
            self.result_label.config(text=f"處理結果時發生錯誤：{str(e)}")
            self.update_preview(None)

    def check_required_columns(self, df):
        """檢查必要的列是否存在"""
        required_columns = ['Modified', 'Item Description', 'Item Code', 
                           'Sale Price', 'Retail', 'Alias']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            error_msg = f"Excel 文件缺少以下必要欄位：\n{', '.join(missing_columns)}"
            messagebox.showerror("錯誤", error_msg)
            return False
        return True

    def refresh_excel(self):
        """刷新當前 Excel 文件"""
        try:
            if not self.current_excel_path:
                messagebox.showwarning("警告", "尚未載入 Excel 文件")
                return
            
            print(f"Refreshing Excel file: {self.current_excel_path}")  # 調試信息
            
            # 重新讀取當前文件
            df = pd.read_excel(self.current_excel_path)
            
            # 檢查必要的列
            if not self.check_required_columns(df):
                return
            
            # 更新數據
            self.df = df
            self.df['Modified'] = pd.to_datetime(self.df['Modified'])
            
            # 清空 Excel 搜尋框
            self.excel_search_entry.delete(0, tk.END)
            
            # 更新預覽
            self.update_excel_preview()
            
            # 更新文件路徑顯示
            self.file_path_entry.config(state='normal')
            self.file_path_entry.delete(0, tk.END)
            self.file_path_entry.insert(0, self.current_excel_path)
            self.file_path_entry.config(state='readonly')
            
            messagebox.showinfo("成功", "Excel 文件已刷新")
            
            # 清空搜尋框並設置焦點
            self.root.after(100, self.clear_and_focus_search_entry)
            
        except Exception as e:
            messagebox.showerror("錯誤", f"刷新文件時發生錯誤：{str(e)}")
            self.clear_and_focus_search_entry()

    def choose_excel_file(self):
        """選擇新的 Excel 文件"""
        try:
            file_path = filedialog.askopenfilename(
                title="選擇 Excel 文件",
                filetypes=[("Excel files", "*.xlsx *.xls")],
                initialdir=os.path.dirname(self.current_excel_path) if self.current_excel_path else None
            )
            
            if not file_path:
                return
            
            print(f"Loading new Excel file: {file_path}")  # 調試信息
            
            # 讀取新文件
            df = pd.read_excel(file_path)  # 修正縮排
            
            # 檢查必要的列
            if not self.check_required_columns(df):
                return
            
            # 更新數據
            self.df = df
            self.df['Modified'] = pd.to_datetime(self.df['Modified'])
            self.current_excel_path = file_path
            
            # 清空 Excel 搜尋框
            self.excel_search_entry.delete(0, tk.END)
            
            # 更新預覽
            self.update_excel_preview()
            
            # 更新文件路徑顯示
            self.file_path_entry.config(state='normal')
            self.file_path_entry.delete(0, tk.END)
            self.file_path_entry.insert(0, file_path)
            self.file_path_entry.config(state='readonly')
            
            messagebox.showinfo("成功", f"已載入新文件：\n{os.path.basename(file_path)}")
            
            # 清空搜尋框並設置焦點
            self.root.after(100, self.clear_and_focus_search_entry)
            
        except Exception as e:
            messagebox.showerror("錯誤", f"載入文件時發生錯誤：{str(e)}")
            self.clear_and_focus_search_entry()

    def sort_treeview(self, col):
        """排序 Treeview 的數據"""
        try:
            # 獲取所有項目
            items = [(self.tree.set(item, col), item) for item in self.tree.get_children('')]
            
            # 確定排序方向
            reverse = False
            if self.sort_state["column"] == col and not self.sort_state["reverse"]:
                reverse = True
            
            # 更新排序狀態
            self.sort_state["column"] = col
            self.sort_state["reverse"] = reverse
            
            # 根據數據類型進行排序
            if col in ["Sale Price", "Retail"]:
                # 價格排序：移除 $ 符號並轉換為浮點數
                items.sort(key=lambda x: float(x[0].replace('$', '').replace(',', '')) if x[0] else 0, 
                          reverse=reverse)
            elif col == "Modified":
                # 日期排序
                items.sort(key=lambda x: pd.to_datetime(x[0]) if x[0] else pd.Timestamp.min, 
                          reverse=reverse)
            else:
                # 字符串排序
                items.sort(key=lambda x: x[0].lower() if x[0] else '', 
                          reverse=reverse)
            
            # 重新排列項目
            for index, (val, item) in enumerate(items):
                self.tree.move(item, '', index)
            
            # 更新列標題顯示排序方向
            for column in self.tree["columns"]:
                if column == col:
                    self.tree.heading(column, 
                                    text=f"{columns_config[column]['text']} {'↓' if reverse else '↑'}")
                else:
                    self.tree.heading(column, 
                                    text=columns_config[column]['text'])
                
        except Exception as e:
            print(f"排序時發生錯誤: {str(e)}")

    def load_latest_excel(self):
        """加載最新的 Excel 文件"""
        try:
            # 從 config.txt 獲取 Excel 目錄路徑
            excel_dir = self.config.get('file_paths', 'excel_directory')
            print(f"Loading Excel from directory: {excel_dir}")
            
            if not os.path.exists(excel_dir):
                messagebox.showerror("錯誤", f"Excel 目錄不存在：{excel_dir}")
                return False
            
            # 獲取目錄中最新的 Excel 文件
            excel_files = glob.glob(os.path.join(excel_dir, "*.xlsx"))
            if not excel_files:
                messagebox.showwarning("警告", f"在 {excel_dir} 目錄中找不到 Excel 文件")
                return False
            
            # 找到最新的文件
            latest_excel = max(excel_files, key=os.path.getmtime)
            print(f"Latest Excel file found: {latest_excel}")
            
            # 保存當前 Excel 文件路徑
            self.current_excel_path = latest_excel
            
            try:
                # 讀取 Excel 文件
                df = pd.read_excel(latest_excel)
                
                # 檢查必要的列
                if not self.check_required_columns(df):
                    return False
                
                # 處理日期列
                df['Modified'] = pd.to_datetime(df['Modified'])
                
                # 保存原始數據
                self.original_df = df.copy()
                self.df = df.copy()
                
                print(f"Successfully loaded Excel with {len(self.df)} rows")
                
                # 更新文件路徑顯示
                self.file_path_entry.config(state='normal')
                self.file_path_entry.delete(0, tk.END)
                self.file_path_entry.insert(0, latest_excel)
                self.file_path_entry.config(state='readonly')
                
                # 設置日期選擇器的初始範圍
                max_date = df['Modified'].max()
                start_date = max_date - pd.Timedelta(weeks=3)  # 設置為最新日期的三週前
                
                # 設置日期選擇器
                self.start_date.set_date(start_date.date())
                self.end_date.set_date(max_date.date())
                
                # 自動應用日期過濾
                self.apply_date_filter()
                
                # 確保更新完成
                self.root.update_idletasks()
                
                return True
                
            except Exception as e:
                print(f"Error reading Excel file: {str(e)}")
                messagebox.showerror("錯誤", f"讀取 Excel 文件時發生錯誤：{str(e)}")
                return False
                
            except Exception as e:
                print(f"Error loading Excel: {str(e)}")
                messagebox.showerror("錯誤", f"加載 Excel 文件時發生錯誤：{str(e)}")
                return False
                
        except Exception as e:
            print(f"Error loading Excel: {str(e)}")
            messagebox.showerror("錯誤", f"加載 Excel 文件時發生錯誤：{str(e)}")
            return False

    def focus_search_entry(self):
        """將焦點設置到標籤搜尋框"""
        self.search_entry.focus_set()
        self.search_entry.select_range(0, tk.END)  # 可選：選中所有文字

    def clear_and_focus_search_entry(self):
        """清空搜尋框並設置焦點"""
        self.search_entry.delete(0, tk.END)
        self.search_entry.focus_set()

    def clear_main_search_entry(self):
        """只清空主搜尋框"""
        self.search_entry.delete(0, tk.END)
        self.search_entry.focus_set()

    def create_non_ethicals_controls(self):
        """創建非 Ethicals 調整控制項"""
        try:
            # 非 Ethicals 控制框架
            self.non_ethicals_frame = tk.LabelFrame(
                self.left_frame,
                text=" 非 Ethicals 調整 ",
                font=("Arial", 12, "bold"),
                bg='white',
                fg='#2c3e50'
            )
            
            # 設置固定大小
            self.non_ethicals_frame.pack(fill=tk.X, padx=5, pady=(5, 0))
            self.non_ethicals_frame.pack_propagate(False)  # 禁止自動調整大小
            self.non_ethicals_frame.configure(height=200)  # 改為 200 像素高
            
            # 條碼位置調整
            barcode_frame = tk.Frame(self.non_ethicals_frame, bg='white')
            barcode_frame.pack(fill=tk.X, padx=5, pady=2)
            
            tk.Label(
                barcode_frame,
                text="條碼位置 (Y):",
                font=("Arial", 10),
                bg='white'
            ).pack(side=tk.LEFT)
            
            self.non_ethicals_position_spinbox = ttk.Spinbox(
                barcode_frame,
                from_=-200,
                to=200,
                increment=5,
                width=8
            )
            self.non_ethicals_position_spinbox.pack(side=tk.LEFT, padx=2)

            # 商品描述位置調整
            desc_frame = tk.Frame(self.non_ethicals_frame, bg='white')
            desc_frame.pack(fill=tk.X, padx=5, pady=2)
            
            tk.Label(
                desc_frame,
                text="商品描述位置 (Y):",
                font=("Arial", 10),
                bg='white'
            ).pack(side=tk.LEFT)
            
            self.non_ethicals_desc_y_spinbox = ttk.Spinbox(
                desc_frame,
                from_=0,
                to=300,
                increment=5,
                width=8
            )
            self.non_ethicals_desc_y_spinbox.pack(side=tk.LEFT, padx=2)

            # 零售價位置調整
            retail_frame = tk.Frame(self.non_ethicals_frame, bg='white')
            retail_frame.pack(fill=tk.X, padx=5, pady=2)
            
            tk.Label(
                retail_frame,
                text="零售價位置 (Y):",
                font=("Arial", 10),
                bg='white'
            ).pack(side=tk.LEFT)
            
            self.non_ethicals_retail_y_spinbox = ttk.Spinbox(
                retail_frame,
                from_=0,
                to=300,
                increment=5,
                width=8
            )
            self.non_ethicals_retail_y_spinbox.pack(side=tk.LEFT, padx=2)

            # 添加字體大小調整
            # 商品描述字體大小調整
            desc_font_frame = tk.Frame(self.non_ethicals_frame, bg='white')
            desc_font_frame.pack(fill=tk.X, padx=5, pady=2)
            
            tk.Label(
                desc_font_frame,
                text="商品描述字體大小:",
                font=("Arial", 10),
                bg='white'
            ).pack(side=tk.LEFT)
            
            self.non_ethicals_desc_font_spinbox = ttk.Spinbox(
                desc_font_frame,
                from_=10,
                to=100,
                increment=1,
                width=8
            )
            self.non_ethicals_desc_font_spinbox.pack(side=tk.LEFT, padx=2)

            # 零售價字體大小調整
            retail_font_frame = tk.Frame(self.non_ethicals_frame, bg='white')
            retail_font_frame.pack(fill=tk.X, padx=5, pady=2)
            
            tk.Label(
                retail_font_frame,
                text="零售價字體大小:",
                font=("Arial", 10),
                bg='white'
            ).pack(side=tk.LEFT)
            
            self.non_ethicals_retail_font_spinbox = ttk.Spinbox(
                retail_font_frame,
                from_=10,
                to=200,
                increment=1,
                width=8
            )
            self.non_ethicals_retail_font_spinbox.pack(side=tk.LEFT, padx=2)

            # 添加 RRP 位置調整
            rrp_frame = tk.Frame(self.non_ethicals_frame, bg='white')
            rrp_frame.pack(fill=tk.X, padx=5, pady=2)
            
            tk.Label(
                rrp_frame,
                text="RRP 位置 (X, Y):",
                font=("Arial", 10),
                bg='white'
            ).pack(side=tk.LEFT)
            
            self.non_ethicals_rrp_x_spinbox = ttk.Spinbox(
                rrp_frame,
                from_=-500,  # 擴大範圍從 -200 到 -500
                to=500,      # 擴大範圍從 200 到 500
                increment=5,
                width=8
            )
            self.non_ethicals_rrp_x_spinbox.pack(side=tk.LEFT, padx=2)
            
            self.non_ethicals_rrp_y_spinbox = ttk.Spinbox(
                rrp_frame,
                from_=-500,  # 擴大範圍從 -300 到 -500
                to=500,      # 擴大範圍從 0 到 500
                increment=5,
                width=8
            )
            self.non_ethicals_rrp_y_spinbox.pack(side=tk.LEFT, padx=2)

            # 添加日期位置調整
            date_frame = tk.Frame(self.non_ethicals_frame, bg='white')
            date_frame.pack(fill=tk.X, padx=5, pady=2)
            
            tk.Label(
                date_frame,
                text="日期位置 (X, Y):",
                font=("Arial", 10),
                bg='white'
            ).pack(side=tk.LEFT)
            
            self.non_ethicals_date_x_spinbox = ttk.Spinbox(
                date_frame,
                from_=-500,  # 擴大範圍從 -100 到 -500
                to=500,      # 擴大範圍從 100 到 500
                increment=5,
                width=8
            )
            self.non_ethicals_date_x_spinbox.pack(side=tk.LEFT, padx=2)
            
            self.non_ethicals_date_y_spinbox = ttk.Spinbox(
                date_frame,
                from_=-500,  # 擴大範圍從 -200 到 -500
                to=500,      # 擴大範圍從 0 到 500
                increment=5,
                width=8
            )
            self.non_ethicals_date_y_spinbox.pack(side=tk.LEFT, padx=2)

            # 綁定事件
            print("綁定 Spinbox 事件")
            for spinbox in [
                self.non_ethicals_position_spinbox,
                self.non_ethicals_desc_y_spinbox,
                self.non_ethicals_retail_y_spinbox,
                self.non_ethicals_desc_font_spinbox,
                self.non_ethicals_retail_font_spinbox,
                self.non_ethicals_rrp_x_spinbox,      # 添加新的 spinbox
                self.non_ethicals_rrp_y_spinbox,      # 添加新的 spinbox
                self.non_ethicals_date_x_spinbox,     # 添加新的 spinbox
                self.non_ethicals_date_y_spinbox      # 添加新的 spinbox
            ]:
                spinbox.bind('<Return>', self.update_non_ethicals_settings)
                spinbox.bind('<<Increment>>', self.update_non_ethicals_settings)
                spinbox.bind('<<Decrement>>', self.update_non_ethicals_settings)
                spinbox.bind('<FocusOut>', self.update_non_ethicals_settings)

            print("非 Ethicals 控制項創建完成")
            # 初始時隱藏
            self.non_ethicals_frame.pack_forget()
            print("控制項已初始隱藏")

        except Exception as e:
            print(f"創建非 Ethicals 控制項時發生錯誤: {str(e)}")
            traceback.print_exc()

    def update_non_ethicals_settings(self, event=None):
        """更新非 Ethicals 設定"""
        print("\n=== 更新非 Ethicals 設定 ===")
        try:
            # 保存當前搜尋文字
            current_search = self.search_entry.get().strip()
            hidden_search = self.hidden_search_entry.get().strip()
            
            # 獲取所有當前值
            settings = {
                'barcode_options': {
                    'position_offset_y': self.non_ethicals_position_spinbox.get()
                },
                'text_offsets': {
                    'item_description_offset_y': self.non_ethicals_desc_y_spinbox.get(),
                    'retail_offset_y': self.non_ethicals_retail_y_spinbox.get(),
                    'rrp_offset_x': self.non_ethicals_rrp_x_spinbox.get(),      # 確保保存 RRP X
                    'rrp_offset_y': self.non_ethicals_rrp_y_spinbox.get(),      # 確保保存 RRP Y
                    'date_offset_x': self.non_ethicals_date_x_spinbox.get(),    # 確保保存日期 X
                    'date_offset_y': self.non_ethicals_date_y_spinbox.get()     # 確保保存日期 Y
                },
                'font_sizes': {
                    'item_description': self.non_ethicals_desc_font_spinbox.get(),
                    'retail': self.non_ethicals_retail_font_spinbox.get()
                }
            }
            
            # 更新配置文件
            for section, values in settings.items():
                if not self.config.has_section(section):
                    self.config.add_section(section)
                for key, value in values.items():
                    self.config.set(section, key, str(value))
            
            # 保存配置到文件
            with open('config.txt', 'w', encoding='utf-8') as configfile:
                self.config.write(configfile)
            print("配置文件已更新")
            
            # 使用隱藏搜尋框的值重新生成標籤
            search_text = hidden_search if hidden_search else current_search
            if search_text:
                print(f"使用搜尋文字重新生成標籤: {search_text}")
                self.search_entry.delete(0, tk.END)
                self.search_entry.insert(0, search_text)
                self.search_and_print(keep_search_text=True)
                
        except Exception as e:
            print(f"更新非 Ethicals 設定時發生錯誤: {str(e)}")
            traceback.print_exc()

    def apply_date_filter(self, event=None):
        """應用日期過濾"""
        try:
            print("\n=== 應用日期過濾 ===")
            
            # 獲取當前的搜尋文字
            search_text = self.excel_search_entry.get().lower().strip()
            
            # 獲取原始的完整數據集
            if hasattr(self, 'original_df'):
                self.df = self.original_df.copy()
            else:
                self.original_df = self.df.copy()
            
            # 獲取選擇的日期範圍
            start_date = pd.to_datetime(self.start_date.get_date())  # 轉換為 datetime
            end_date = pd.to_datetime(self.end_date.get_date())  # 轉換為 datetime
            end_date = end_date + pd.Timedelta(days=1) - pd.Timedelta(microseconds=1)  # 設置為當天的最後一刻
            
            print(f"過濾日期範圍: 從 {start_date} 到 {end_date}")
            
            # 確保結束日期不早於開始日期
            if end_date < start_date:
                messagebox.showwarning("警告", "結束日期不能早於開始日期")
                return
            
            # 在 DataFrame 中過濾日期
            mask = (pd.to_datetime(self.df['Modified']) >= start_date) & \
                   (pd.to_datetime(self.df['Modified']) <= end_date)
            
            # 如果有搜尋文字，添加搜尋條件
            if search_text:
                search_mask = self.df.apply(lambda row: any(
                    str(value).lower().find(search_text) != -1 
                    for value in row.values if pd.notnull(value)
                ), axis=1)
                mask = mask & search_mask
            
            filtered_df = self.df[mask]
            
            # 更新 Treeview
            self.update_treeview_with_data(filtered_df)
            
            print(f"找到 {len(filtered_df)} 個符合日期範圍的項目")
            
        except Exception as e:
            print(f"應用日期過濾時發生錯誤: {str(e)}")
            traceback.print_exc()

    def clear_date_filter(self):
        """清除日期過濾"""
        try:
            print("\n=== 清除日期過濾 ===")
            
            # 恢復原始數據
            if hasattr(self, 'original_df'):
                self.df = self.original_df.copy()
            
            # 獲取數據中的最早和最晚日期
            min_date = pd.to_datetime(self.df['Modified']).min()
            max_date = pd.to_datetime(self.df['Modified']).max()
            
            # 設置日期選擇器的範圍
            self.start_date.set_date(min_date.date())
            self.end_date.set_date(max_date.date())
            
            # 更新顯示
            self.update_excel_preview()
            
            print("日期過濾已清除，顯示所有數據")
            
        except Exception as e:
            print(f"清除日期過濾時發生錯誤: {str(e)}")
            traceback.print_exc()

    def update_treeview_with_data(self, df):
        """更新 Treeview 顯示"""
        # 清空現有的顯示
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 添加新數據
        for _, row in df.iterrows():
            try:
                modified_date = pd.to_datetime(row['Modified']).strftime('%Y-%m-%d %H:%M')
                sale_price = f"${float(row['Sale Price']):.2f}" if pd.notnull(row['Sale Price']) else ''
                retail = f"${float(row['Retail']):.2f}" if pd.notnull(row['Retail']) else ''
                
                values = (
                    modified_date,
                    str(row['Item Description']),
                    str(row['Item Code']),
                    sale_price,
                    retail,
                    str(row.get('Alias', ''))
                )
                
                self.tree.insert('', 'end', values=values)
            except Exception as e:
                print(f"處理行時發生錯誤: {str(e)}")
                continue

def main():
    root = tk.Tk()
    app = TicketGeneratorGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()

