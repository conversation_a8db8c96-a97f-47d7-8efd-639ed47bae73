#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 Ethical 標籤條碼調整功能修復
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ticket_generator'))

from utils.config_manager import ConfigManager
from core.label_generator import LabelGenerator
from models.data_models import ProductItem
from datetime import datetime

def test_ethical_barcode_adjustment():
    """測試 Ethical 標籤的條碼調整功能"""
    print("=== 測試 Ethical 標籤條碼調整功能 ===\n")
    
    # 初始化組件
    config_manager = ConfigManager()
    label_generator = LabelGenerator(config_manager)
    
    # 創建測試用的 Ethical 商品
    ethical_item = ProductItem(
        item_code="TEST001",
        item_description="測試 Ethical 商品",
        department="Ethicals",
        retail=29.99,
        sale_price=25.99,
        rrp=None,
        modified_date=datetime.now()
    )
    
    print(f"測試商品: {ethical_item.item_code} - {ethical_item.item_description}")
    print(f"部門: {ethical_item.department}")
    print()
    
    # 測試不同的條碼設置
    test_settings = [
        {
            'name': 'default',
            'position_offset_x': 0,
            'position_offset_y': 65,
            'width_scale': 0.8,
            'height_scale': 0.5
        },
        {
            'name': 'moved_right',
            'position_offset_x': 50,
            'position_offset_y': 65,
            'width_scale': 0.8,
            'height_scale': 0.5
        },
        {
            'name': 'moved_left',
            'position_offset_x': -50,
            'position_offset_y': 65,
            'width_scale': 0.8,
            'height_scale': 0.5
        },
        {
            'name': 'larger_size',
            'position_offset_x': 0,
            'position_offset_y': 65,
            'width_scale': 1.0,
            'height_scale': 0.8
        },
        {
            'name': 'smaller_size',
            'position_offset_x': 0,
            'position_offset_y': 65,
            'width_scale': 0.6,
            'height_scale': 0.3
        }
    ]
    
    print("開始測試不同的條碼設置...")
    
    for setting in test_settings:
        print(f"  測試設置: {setting['name']}")
        print(f"    X位置: {setting['position_offset_x']}")
        print(f"    Y位置: {setting['position_offset_y']}")
        print(f"    寬度比例: {setting['width_scale']}")
        print(f"    高度比例: {setting['height_scale']}")
        
        # 更新 Ethical 設置
        config_manager.update_config('barcode_options_ethicals', 'position_offset_x', setting['position_offset_x'])
        config_manager.update_config('barcode_options_ethicals', 'position_offset_y', setting['position_offset_y'])
        config_manager.update_config('barcode_options_ethicals', 'width_scale', setting['width_scale'])
        config_manager.update_config('barcode_options_ethicals', 'height_scale', setting['height_scale'])
        config_manager.reload_config()
        
        try:
            # 生成 Ethical 標籤
            image = label_generator.create_label(ethical_item)
            output_path = f"test_ethical_barcode_{setting['name']}.png"
            image.save(output_path)
            print(f"    ✓ 已生成: {output_path}")
        except Exception as e:
            print(f"    ✗ 錯誤: {str(e)}")
        
        print()
    
    # 驗證配置讀取
    print("--- 驗證配置讀取 ---")
    ethical_settings = config_manager.get_ethicals_settings()
    
    print("Ethical 條碼設置:")
    print(f"  X位置偏移: {ethical_settings['barcode_options']['position_offset_x']}")
    print(f"  Y位置偏移: {ethical_settings['barcode_options']['position_offset_y']}")
    print(f"  寬度比例: {ethical_settings['barcode_options']['width_scale']}")
    print(f"  高度比例: {ethical_settings['barcode_options']['height_scale']}")
    
    print("\n=== 測試完成 ===")
    print("✅ Ethical 標籤條碼調整功能已修復")
    print("📋 現在可調整的項目:")
    print("  • 條碼位置 (X軸) - 新增")
    print("  • 條碼位置 (Y軸)")
    print("  • 條碼寬度比例")
    print("  • 條碼高度比例")
    print("  • 文字位置 (X軸、Y軸)")
    print("  • 字體大小")

if __name__ == "__main__":
    test_ethical_barcode_adjustment()
