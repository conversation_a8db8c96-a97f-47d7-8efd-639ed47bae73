#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 Ethicals 條碼調整功能
"""

import sys
import os
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.label_generator import LabelGenerator
from models.data_models import ProductItem
from utils.config_manager import ConfigMana<PERSON>

def test_ethicals_barcode_adjustment():
    """測試 Ethicals 條碼調整功能"""
    print("=== 測試 Ethicals 條碼調整功能 ===")
    
    # 創建配置管理器
    config_manager = ConfigManager()
    
    # 創建標籤生成器
    label_generator = LabelGenerator(config_manager)
    
    # 創建測試商品（Ethicals）
    test_item = ProductItem(
        item_code="123456",
        item_description="PANADOL EXTRA STRENGTH 24 TABLETS",
        department="ethicals",  # Ethicals 部門
        retail=12.99,
        sale_price=12.99,
        rrp=15.99,
        modified_date=datetime(2025, 1, 7)
    )
    
    print(f"測試商品: {test_item.item_description}")
    print(f"商品代碼: {test_item.item_code}")
    print(f"部門: {test_item.department}")
    print(f"零售價: ${test_item.retail:.2f}")
    
    # 測試原始設置
    print(f"\n--- 測試原始 Ethicals 設置 ---")
    try:
        image = label_generator.create_label(test_item, 'ethicals')
        image.save("test_ethicals_original.png")
        print("✓ 已生成原始 Ethicals 標籤: test_ethicals_original.png")
    except Exception as e:
        print(f"✗ 生成原始標籤時發生錯誤: {str(e)}")
    
    # 測試條碼寬度調整
    print(f"\n--- 測試條碼寬度調整 ---")
    width_values = [0.6, 0.8, 1.0]
    
    for width in width_values:
        print(f"  設置條碼寬度比例: {width}")
        config_manager.update_config('barcode_options_ethicals', 'width_scale', str(width))
        config_manager.reload_config()
        
        try:
            image = label_generator.create_label(test_item, 'ethicals')
            image.save(f"test_ethicals_width_{width}.png")
            print(f"  ✓ 已生成標籤: test_ethicals_width_{width}.png")
        except Exception as e:
            print(f"  ✗ 生成標籤時發生錯誤: {str(e)}")
    
    # 測試條碼高度調整
    print(f"\n--- 測試條碼高度調整 ---")
    height_values = [0.6, 0.8, 1.0]
    
    for height in height_values:
        print(f"  設置條碼高度比例: {height}")
        config_manager.update_config('barcode_options_ethicals', 'height_scale', str(height))
        config_manager.reload_config()
        
        try:
            image = label_generator.create_label(test_item, 'ethicals')
            image.save(f"test_ethicals_height_{height}.png")
            print(f"  ✓ 已生成標籤: test_ethicals_height_{height}.png")
        except Exception as e:
            print(f"  ✗ 生成標籤時發生錯誤: {str(e)}")
    
    # 測試條碼位置調整
    print(f"\n--- 測試條碼位置調整 ---")
    position_values = [50, 70, 90]
    
    for position in position_values:
        print(f"  設置條碼位置 Y: {position}")
        config_manager.update_config('barcode_options_ethicals', 'position_offset_y', str(position))
        config_manager.reload_config()
        
        try:
            image = label_generator.create_label(test_item, 'ethicals')
            image.save(f"test_ethicals_position_{position}.png")
            print(f"  ✓ 已生成標籤: test_ethicals_position_{position}.png")
        except Exception as e:
            print(f"  ✗ 生成標籤時發生錯誤: {str(e)}")
    
    # 恢復推薦設置
    print(f"\n--- 設置推薦的 Ethicals 條碼設置 ---")
    config_manager.update_config('barcode_options_ethicals', 'width_scale', '0.8')
    config_manager.update_config('barcode_options_ethicals', 'height_scale', '0.8')
    config_manager.update_config('barcode_options_ethicals', 'position_offset_y', '65')
    
    try:
        config_manager.reload_config()
        image = label_generator.create_label(test_item, 'ethicals')
        image.save("test_ethicals_final.png")
        print("✓ 已生成最終 Ethicals 標籤: test_ethicals_final.png")
    except Exception as e:
        print(f"✗ 生成最終標籤時發生錯誤: {str(e)}")
    
    # 驗證設置載入
    print(f"\n--- 驗證設置載入 ---")
    settings = config_manager.get_ethicals_settings()
    print(f"條碼位置 Y: {settings['barcode_options']['position_offset_y']}")
    print(f"條碼寬度比例: {settings['barcode_options']['width_scale']}")
    print(f"條碼高度比例: {settings['barcode_options']['height_scale']}")
    
    print("\n=== 測試完成 ===")
    print("✅ Ethicals 條碼調整功能已修復")
    print("📋 可調整項目:")
    print("  • 條碼位置 (Y軸)")
    print("  • 條碼寬度比例 (50-150)")
    print("  • 條碼高度比例 (40-120)")
    print("  • 文字位置 (X軸、Y軸)")
    print("  • 字體大小")

if __name__ == "__main__":
    test_ethicals_barcode_adjustment()
