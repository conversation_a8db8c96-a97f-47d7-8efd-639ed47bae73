import tkinter as tk
from ui.main_window import Tick<PERSON><PERSON><PERSON><PERSON>G<PERSON>
from utils.excel_handler import <PERSON>celHandler
from utils.printer_manager import PrinterManager
from utils.config_manager import ConfigManager

def main():
    try:
        # 創建配置管理器
        config_manager = ConfigManager()
        
        # 創建 Excel 處理器
        excel_handler = ExcelHandler(config_manager)
        
        # 創建打印管理器
        printer_manager = PrinterManager(config_manager)
        
        # 創建主窗口
        root = tk.Tk()
        app = TicketGeneratorGUI(
            root=root,
            excel_handler=excel_handler,
            printer_manager=printer_manager
        )
        
        root.mainloop()
        
    except Exception as e:
        print(f"程序啟動時發生錯誤: {str(e)}")
        raise

if __name__ == "__main__":
    main() 